import { Any, BinaryMessenger } from '@ohos/flutter_ohos';
import { Disposable } from '../../types/Disposable';
import InAppWebViewInterface from '../InAppWebViewInterface';
import WebMessageListenerChannelDelegate from './WebMessageListenerChannelDelegate';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
export default class WebMessageListener implements Disposable {
    id: string;
    jsObjectName: string;
    allowedOriginRules: Set<string>;
    webView: InAppWebViewInterface | null;
    channelDelegate: WebMessageListenerChannelDelegate | null;
    constructor(id: string, webView: InAppWebViewInterface, messenger: BinaryMessenger | null, jsObjectName: string, allowedOriginRules: Set<string>);
    initJsInstance(): void;
    static fromMap(webView: InAppWebViewInterface, messenger: BinaryMessenger | null, map: Map<string, Any> | null): WebMessageListener | null;
    postMessageForInAppWebView(message: WebMessageCompatExt | null, result: MethodResult): void;
    dispose(): void;
}
