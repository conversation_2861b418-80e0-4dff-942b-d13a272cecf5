import { Any } from '@ohos/flutter_ohos';
export default class WebResourceResponseExt {
    private contentType;
    private contentEncoding;
    private statusCode;
    private reasonPhrase;
    private headers;
    private data;
    constructor(contentType: string, contentEncoding: string, statusCode: number, reasonPhrase: string, headers: Map<string, string>, data: ArrayBuffer);
    static fromWebResourceResponse(response: WebResourceResponse): WebResourceResponseExt;
    static fromMap(map: Map<string, Any>): WebResourceResponseExt | null;
    toMap(): Map<string, Object>;
    getContentType(): string;
    setContentType(contentType: string): void;
    getContentEncoding(): string;
    setContentEncoding(contentEncoding: string): void;
    getStatusCode(): number;
    setStatusCode(statusCode: number): void;
    getReasonPhrase(): string;
    setReasonPhrase(reasonPhrase: string): void;
    getHeaders(): Map<string, string>;
    setHeaders(headers: Map<string, string>): void;
    getData(): ArrayBuffer;
    setData(data: ArrayBuffer): void;
    getHeaderArray(): Array<Header>;
    toString(): string;
}
