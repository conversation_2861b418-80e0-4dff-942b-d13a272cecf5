import common from '@ohos.app.ability.common';
import { ErrorCallback } from './ErrorCallback';
export declare class ServiceManager {
    checkServiceStatus(permission: number, context: common.Context, successCallback: SuccessCallback, errorCallback: ErrorCallback): void;
    private isLocationServiceEnable;
    private isBluetoothServiceEnable;
}
export interface SuccessCallback {
    onSuccess(serviceStatus: number): void;
}
