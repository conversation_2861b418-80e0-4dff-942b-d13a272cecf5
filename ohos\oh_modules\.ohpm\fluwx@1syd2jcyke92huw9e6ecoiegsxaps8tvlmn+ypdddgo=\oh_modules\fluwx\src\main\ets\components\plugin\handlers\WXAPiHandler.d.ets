import * as wechatOpenSDK from "@tencent/wechat_open_sdk";
import { Method<PERSON>all, MethodResult } from '@ohos/flutter_ohos';
import common from "@ohos.app.ability.common";
export declare class WXAPiHandler {
    static wxApi: wechatOpenSDK.WXApi | null;
    private static registered;
    private static context;
    static get wxApiRegistered(): boolean;
    static get uiContext(): import("fluwx/../../../../../../../../softs/DevEco/DevEco Studio/sdk/default/openharmony/ets/api/application/UIAbilityContext").default;
    static coolBoot: boolean;
    static setContext(context: common.UIAbilityContext): void;
    static registerApp(call: MethodCall, result: MethodResult): void;
    static checkWeChatInstallation(result: MethodResult): void;
    private static registerWxAPIInternal;
}
