import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import StandardMessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/StandardMessageCodec';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
import { ByteBuffer } from '@ohos/flutter_ohos/src/main/ets/util/ByteBuffer';
export declare class FlutterError extends Error {
    /** The error code. */
    code: string;
    /** The error details. Must be a datatype supported by the api codec. */
    details: ESObject | null;
    constructor(code: string, message: string, details: ESObject);
}
export declare function wrapError(exception: Error): Array<Object>;
/**
 * Configuration options for an in-app WebView.
 *
 */
export declare class WebViewOptions {
    private enableJavaScript;
    getEnableJavaScript(): boolean;
    setEnableJavaScript(setterArg: boolean): void;
    private enableDomStorage;
    getEnableDomStorage(): boolean;
    setEnableDomStorage(setterArg: boolean): void;
    private headers;
    getHeaders(): Map<string, string>;
    setHeaders(setterArg: Map<string, string>): void;
    toList(): Array<ESObject>;
    static fromList(list: Array<Object>): WebViewOptions;
    /** Constructor is non-public to enforce null safety; use Builder. */
    private WebViewOptions;
}
export declare class UrlLauncherApiCodec extends StandardMessageCodec {
    static INSTANCE: UrlLauncherApiCodec;
    private constructor();
    readValueOfType(type: number, buffer: ByteBuffer): Object;
    writeValue(stream: ByteBuffer, value: ESObject): void;
}
export interface UrlLauncherApi {
    /** Returns true if the URL can definitely be launched. */
    canLaunchUrl(url: string): boolean;
    /** Opens the URL externally, returning true if successful. */
    launchUrl(url: string, headers: Map<string, string>): boolean;
    /** Opens the URL in an in-app WebView, returning true if it opens successfully. */
    openUrlInWebView(url: string, options: WebViewOptions): boolean;
    /** Closes the view opened by [openUrlInSafariViewController]. */
    closeWebView(): boolean;
    /** The codec used by UrlLauncherApi. */
    getCodec(): MessageCodec<ESObject>;
    /** Sets up an instance of `UrlLauncherApi` to handle messages through the `binaryMessenger`. */
    setup(binaryMessenger: BinaryMessenger | null, api: UrlLauncherApi | null): ESObject;
}
