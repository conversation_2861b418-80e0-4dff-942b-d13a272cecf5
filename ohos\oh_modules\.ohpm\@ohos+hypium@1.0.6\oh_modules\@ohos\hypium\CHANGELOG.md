## 1.0.6

- 适配模块化编译
- 适配IDE覆盖率数据输出

## 1.0.5

- 优化接口异常场景处理能力
- 修复大规模压力测试下内存溢出问题
- 修复测试脚本定义扩展原型方法与框架的兼容性问题
- 异步promise状态判断断言与取反断言兼容

## 1.0.4

- 新增异步promise状态判断断言
- 新增基础数据判断断言
- 新增断言异常信息描述
- 优化用例计时逻辑，采用相对时间计时
- 修改用例执行过程，异步执行
- 修复用例结果偶现的乱序问题

## 1.0.3

- 新增mock对象的能力，支持mock无参数函数
- 新增测试套、测试用例随机执行功能
- 解决测试套声明不规范，导致用例加载异常的问题
- 修复命令行窗口输出乱序的问题

## 1.0.2

- 新增mock接口，判断当前mock方法状态、使用次数
- 修复用例耗时统计问题
- 修改断言功能，断言失败后，会抛出异常

## 1.0.1
- 新增mock基础能力
- 修复部分断言失败后，消息提示异常问题

## 1.0.0
- 新增应用日志关键字查询接口，查询hilog日志中是否包含指定字符串
- 支持用例筛选功能，用于指定测试用例的执行。可按用例名称、类型、规模、级别与测试套名称筛选
- 支持新SDK下单元测试
- 支持dry run 功能，通过命令行传参数，返回当前测试套用例名称全集
- 新增框架接口声明文件，用于deveco联想
- 修复用例状态统计问题