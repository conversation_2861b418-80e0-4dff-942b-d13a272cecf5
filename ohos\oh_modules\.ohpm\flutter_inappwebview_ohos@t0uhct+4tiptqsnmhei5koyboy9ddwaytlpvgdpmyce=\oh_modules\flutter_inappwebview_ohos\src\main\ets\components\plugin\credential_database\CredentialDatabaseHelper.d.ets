import { Context as Context } from "@ohos.abilityAccessCtrl";
import data_rdb from '@ohos.data.relationalStore';
export default class CredentialDatabaseHelper {
    private static SQL_CREATE_PROTECTION_SPACE_TABLE;
    private static SQL_CREATE_CREDENTIAL_TABLE;
    private static SQL_DELETE_PROTECTION_SPACE_TABLE;
    private static SQL_DELETE_CREDENTIAL_TABLE;
    private rdbStore;
    private context;
    constructor(context: Context);
    private initRdb;
    clearAllTables(): Promise<void>;
    getRdbStore(): Promise<data_rdb.RdbStore>;
}
