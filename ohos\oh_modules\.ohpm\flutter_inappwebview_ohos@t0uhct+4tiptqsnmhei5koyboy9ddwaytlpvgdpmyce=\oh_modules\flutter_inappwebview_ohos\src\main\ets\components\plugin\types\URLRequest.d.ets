import { Any } from '@ohos/flutter_ohos';
export default class URLRequest {
    private url;
    private method;
    private body;
    private headers;
    constructor(url: string, method: string, body: ArrayBuffer | null, headers: Map<string, string>);
    getUrl(): string;
    setUrl(url: string): void;
    getMethod(): string;
    setMethod(method: string): void;
    getBody(): ArrayBuffer | null;
    setBody(body: ArrayBuffer): void;
    getHeaders(): Map<string, string>;
    setHeaders(headers: Map<string, string>): void;
    static fromMap(map: Map<string, Any>): URLRequest | null;
    toMap(): Map<string, Any>;
}
