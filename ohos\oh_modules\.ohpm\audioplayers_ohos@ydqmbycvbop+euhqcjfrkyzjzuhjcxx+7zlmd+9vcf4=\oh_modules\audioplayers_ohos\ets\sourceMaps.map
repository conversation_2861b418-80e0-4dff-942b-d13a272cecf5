{"audioplayers_ohos|audioplayers_ohos|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|index.ts": {"version": 3, "file": "index.ets", "sourceRoot": "", "sources": ["ohos/index.ets"], "names": [], "mappings": "OAeO,kBAAkB;AACzB,eAAe,kBAAkB,CAAC", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/AudioContextOhos.ts": {"version": 3, "file": "AudioContextOhos.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/AudioContextOhos.ets"], "names": [], "mappings": "YAeO,KAAK;YACL,KAAK;AAGZ,MAAM,CAAC,OAAO,OAAO,gBAAgB;IACnC,aAAa,EAAE,MAAM,GAAG,CAAC,CAAC;IAC1B,SAAS,EAAE,MAAM,GAAG,CAAC,CAAC;IACtB,gBAAgB,EAAE,OAAO,GAAG,IAAI,CAAC;IACjC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC;IACvB,SAAS,EAAE,OAAO,GAAG,KAAK,CAAC;IAE3B,IAAI,IAAI,gBAAgB;QACtB,IAAI,OAAO,GAAG,IAAI,gBAAgB,EAAE,CAAA;QACpC,OAAO,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC;QAC3C,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,OAAO,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QACjD,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;QACrC,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACnC,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,qBAAqB,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ;QAC/C,WAAW,CAAC,iBAAiB,GAAG;YAC9B,OAAO,EAAE,CAAC;YACV,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,IAAI,CAAC,aAAa;SACzD,CAAA;IACH,CAAC;IAED,eAAe,IAAI,KAAK,CAAC,iBAAiB;QACxC,OAAO;YACL,OAAO,EAAE,CAAC;YACV,KAAK,EAAE,IAAI,CAAC,SAAS;YACrB,aAAa,EAAE,IAAI,CAAC,aAAa;SAClC,CAAA;IACH,CAAC;IAED,MAAM,CAAC,OAAO,EAAE,gBAAgB;QAC9B,OAAO,OAAO,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC;IAC5F,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/AudioplayersPlugin.ts": {"version": 3, "file": "AudioplayersPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/AudioplayersPlugin.ets"], "names": [], "mappings": "cAgBE,aAAa,EACb,oBAAoB;OAEf,aAAgC;cAAf,YAAY;cAC3B,SAAS,EAAE,aAAa;YAC1B,UAAU;OACV,EAAuD,YAAY,EAAE,GAAG,EAAE;cAAxE,YAAY,EAAE,oBAAoB,EAAE,eAAe;OACnD,OAAO;OACT,gBAAgB;OAChB,EAAE,gBAAgB,EAAE;OACpB,aAAa;OACb,KAAK;OACL,SAAS;OACT,WAAW;OACX,EAAE,gBAAgB,EAAE;OACpB,EAAE,eAAe,EAAE;cACjB,SAAS,IAAT,SAAS;OAAE,SAAS;OACpB,qBAAqB;cACrB,aAAa,IAAb,aAAa;OACA,gBAAgB;AAEtC,MAAM,GAAG,GAAG,oBAAoB,CAAC;AAEjC,MAAM,CAAC,OAAO,OAAO,kBAAmB,YAAW,aAAa,EAAE,YAAY,EAAE,eAAe;IAC7F,OAAO,CAAC,OAAO,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC;IAC5C,OAAO,CAAC,aAAa,CAAC,EAAE,aAAa,GAAG,SAAS,CAAC;IAClD,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;IAChD,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,CAAC;IACtC,OAAO,CAAC,eAAe,CAAC,EAAE,eAAe,GAAG,SAAS,CAAC;IACtD,OAAO,CAAC,gBAAgB,CAAC,EAAE,gBAAgB,GAAG,SAAS,CAAC;IACxD,OAAO,CAAC,OAAO,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,aAAa,GAAG,CAAC;IACvD,OAAO,CAAC,mBAAmB,GAAG,IAAI,gBAAgB,EAAE,CAAC;IACrD,OAAO,CAAC,cAAc,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAC7C,OAAO,CAAC,cAAc,CAAC,EAAE,oBAAoB,GAAG,SAAS,CAAC;IAC1D,OAAO,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,SAAS,GAAG,SAAS,CAAC;IACzD,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;IAEtC,kBAAkB,IAAI,MAAM;QAC1B,OAAO,oBAAoB,CAAA;IAC7B,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,qBAAqB,EAAE,CAAA;QAC9C,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,kBAAkB,EAAE,CAAA;QACnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAA;QAClD,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,uBAAuB,CAAC,CAAA;QACvF,IAAI,CAAC,OAAO,CAAC,oBAAoB,CAAC;YAChC,YAAY,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;gBACvD,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YAC/B,CAAC;SACF,CAAC,CAAA;QACF,IAAI,CAAC,aAAa,GAAG,IAAI,aAAa,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,8BAA8B,CAAC,CAAA;QACpG,IAAI,CAAC,aAAa,CAAC,oBAAoB,CAAC;YACtC,YAAY,EAAE,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE;gBACvD,IAAI,CAAC,gBAAgB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAA;YACrC,CAAC;SACF,CAAC,CAAA;QACF,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,EAAE,qCAAqC,CAAC,CAAC,CAAA;IAC7H,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACvD,IAAI,CAAC,WAAW,EAAE,CAAA;QAClB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QAC5E,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAA;QACpB,IAAI,CAAC,gBAAgB,EAAE,OAAO,EAAE,CAAA;QAChC,IAAI,CAAC,YAAY,EAAE,OAAO,EAAE,CAAA;QAC5B,IAAI,CAAC,WAAW,EAAE,CAAC;IACrB,CAAC;IAED,OAAO,CAAC,gBAAgB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACpE,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,iBAAiB;gBACtB,eAAe;gBACf,2CAA2C;gBAC3C,mDAAmD;gBACnD,sEAAsE;gBACpE,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBACrD,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,qBAAqB,CAAC,IAAI,MAAM,CAAC;gBACpF,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,CAAC;gBAC9B,MAAM;YACR,KAAK,WAAW;gBACd,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,CAAC,IAAI,MAAM,CAAC;gBAC3E,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,qBAAqB,CAAC,IAAI,MAAM,CAAC;gBAChF,IAAI,CAAC,iBAAiB,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;gBAC3C,MAAM;YACR;gBACE,MAAM,CAAC,cAAc,EAAE,CAAA;gBACvB,MAAM;SACT;IACH,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,QAAQ,EAAE,YAAY;QAC/D,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,MAAM,CAAA;QAClD,IAAI,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,SAAS,EAAE;YAC7C,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACnB,OAAO;SACR;QACD,IAAI;YACF,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,EAAE;gBAC3B,IAAI,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC,EAAE,gCAAgC,QAAQ,EAAE,CAAC,CAAC,CAAA;gBACxH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,aAAa,CAAC,IAAI,EAAE,YAAY,EAAE,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;gBAC1H,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;gBACpB,OAAO;aACR;YACD,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;YACrC,IAAI,MAAM,IAAI,IAAI,EAAE;gBAClB,QAAQ,CAAC,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC,CAAA;gBAC7E,OAAO;aACR;YACD,QAAQ,IAAI,CAAC,MAAM,EAAE;gBACnB,KAAK,cAAc;oBACjB,IAAI,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,EAAE,iBAAiB,CAAC,IAAI,MAAM,CAAC;oBACxE,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,IAAI,OAAO,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;oBAC1F,MAAM,MAAM,CAAC,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,CAAC;oBACrD,MAAM;gBACR,KAAK,gBAAgB;oBACnB,IAAI,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,OAAO,EAAE,oBAAoB,CAAC,IAAI,WAAW,CAAA;oBACnF,MAAM,CAAC,SAAS,CAAC,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC,CAAA;oBACxC,MAAM;gBACR,KAAK,QAAQ;oBACX,MAAM,CAAC,IAAI,EAAE,CAAC;oBACd,MAAM;gBACR,KAAK,OAAO;oBACV,MAAM,CAAC,KAAK,EAAE,CAAC;oBACf,MAAM;gBACR,KAAK,MAAM;oBACT,MAAM,CAAC,IAAI,EAAE,CAAC;oBACd,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,CAAC,OAAO,EAAE,CAAC;oBACjB,MAAM;gBACR,KAAK,MAAM;oBACT,IAAI,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,sBAAsB,CAAC,IAAI,MAAM,CAAC;oBACvF,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBACtB,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE,oBAAoB,CAAC,IAAI,MAAM,CAAC;oBACjF,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;oBACzB,MAAM;gBACR,KAAK,YAAY;oBACf,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,qBAAqB,CAAC,IAAI,MAAM,CAAC;oBACpF,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;oBAC3B,MAAM;gBACR,KAAK,iBAAiB;oBACpB,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,cAAc,EAAE,0BAA0B,CAAC,IAAI,MAAM,CAAC;oBAC3F,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;oBACrB,MAAM;gBACR,KAAK,aAAa;oBAChB,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;oBACpC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;oBAC1C,OAAO;gBACT,KAAK,oBAAoB;oBACvB,IAAI,OAAO,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;oBAC1C,QAAQ,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;oBACvC,OAAO;gBACT,KAAK,gBAAgB;oBACnB,IAAI,WAAW,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,aAAa,EAAE,yBAAyB,CAAC,IAAI,MAAM,CAAA;oBAC/F,MAAM,CAAC,cAAc,CAAC,gBAAgB,CAAC,WAAW,CAAC,CAAC,CAAC;oBACrD,MAAM;gBACR,KAAK,eAAe;oBAClB,IAAI,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,wBAAwB,CAAC,IAAI,MAAM,CAAA;oBAC5F,MAAM,CAAC,aAAa,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;oBAClD,MAAM;gBACR,KAAK,iBAAiB;oBACpB,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;oBAC9C,MAAM,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAA;oBACvC,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,qBAAqB,CAAC,IAAI,MAAM,CAAA;oBACnF,MAAM,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;oBACzB,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,EAAE,kBAAkB,CAAC,IAAI,MAAM,CAAA;oBAC1E,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,SAAS,EAAE,qBAAqB,CAAC,IAAI,MAAM,CAAA;oBAC/E,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;oBACvC,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM,CAAC,OAAO,EAAE,CAAA;oBAChB,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;oBAC7B,MAAM;gBACR;oBACE,QAAQ,CAAC,cAAc,EAAE,CAAA;oBACzB,OAAO;aACV;YACD,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;SACpB;QAAC,OAAO,CAAC,EAAE;YACV,QAAQ,CAAC,KAAK,CAAC,gBAAgB,EAAE,CAAC,CAAC,OAAO,EAAE,CAAC,CAAC,CAAA;SAC/C;IACH,CAAC;IAED,OAAO,CAAC,SAAS,CAAC,QAAQ,EAAE,MAAM,GAAG,aAAa,GAAG,IAAI;QACvD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YACjC,OAAO,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAA;SAClC;QACD,IAAI,CAAC,KAAK,CAAC,+DAA+D,CAAC,CAAA;QAC3E,OAAO,IAAI,CAAA;IACb,CAAC;IAED,qBAAqB,IAAI,OAAO;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAA;IACtB,CAAC;IAED,eAAe,IAAI,KAAK,CAAC,YAAY;QACnC,OAAO,KAAK,CAAC,eAAe,EAAE,CAAC;IACjC,CAAC;IAED,eAAe;QACb,IAAI,CAAC,YAAY,EAAE,CAAA;IACrB,CAAC;IAED,cAAc,CAAC,MAAM,EAAE,aAAa;QAClC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;QACpC,IAAI,QAAQ,GAAG,MAAM,CAAC,WAAW,EAAE,CAAC;QACpC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IAED,cAAc,CAAC,MAAM,EAAE,aAAa;QAClC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;IAClD,CAAC;IAED,cAAc,CAAC,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO;QACvD,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,OAAO,GAAG,CAAC;QACrC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC;QAC7B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IAED,SAAS,CAAC,MAAM,EAAE,aAAa,EAAE,OAAO,EAAE,MAAM;QAC9C,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;QACpC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1B,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IAClD,CAAC;IAED,eAAe,CAAC,OAAO,EAAE,MAAM;QAC7B,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;QACpC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1B,IAAI,CAAC,YAAY,CAAC,CAAC,OAAO,CAAC,aAAa,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,WAAW,CAAC,MAAM,EAAE,aAAa,EAAE,SAAS,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,KAAU;QACnG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;IAClE,CAAC;IAED,iBAAiB,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,KAAU;QAClF,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;IACjE,CAAC;IAED,kBAAkB,CAAC,MAAM,EAAE,aAAa;QACtC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;QACpC,IAAI,QAAQ,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC1C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,sBAAsB,CAAC,CAAA;QACnD,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAA;IAC7D,CAAC;IAED,YAAY;QACV,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;YACrC,IAAI,CAAC,cAAc,EAAE,CAAC;QACxB,CAAC,EAAE,GAAG,CAAC,CAAA;IACT,CAAC;IAED,WAAW;QACT,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,aAAa,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;YAClC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAA;SAC3B;IACH,CAAC;IAED,OAAO,CAAC,cAAc;QACpB,IAAI,YAAY,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC;QAC3B,IAAI,YAAY,IAAI,IAAI,IAAI,OAAO,IAAI,IAAI,EAAE;YAC3C,IAAI,CAAC,WAAW,EAAE,CAAA;YAClB,OAAM;SACP;QACD,IAAI,YAAY,GAAG,KAAK,CAAA;QACxB,YAAY,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE;YAC1D,IAAI,MAAM,CAAC,iBAAiB,EAAE,EAAE;gBAC9B,YAAY,GAAG,IAAI,CAAA;gBACnB,IAAI,IAAI,GAAG,MAAM,CAAC,kBAAkB,EAAE,CAAA;gBAEtC,IAAI,GAAG,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC;gBACpC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAClC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,yBAAyB,EAAE,GAAG,CAAC,CAAA;aAC5D;QACH,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,YAAY,EAAE;YACjB,IAAI,CAAC,WAAW,EAAE,CAAA;SACnB;IACH,CAAC;IAED,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,GAAG,gBAAgB;QACzD,IAAI,WAAW,GAAG,IAAI,gBAAgB,EAAE,CAAC;QACzC,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,EAAE,8BAA8B,CAAC,IAAI,OAAO,CAAA;QACvH,WAAW,CAAC,aAAa,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,eAAe,EAAE,2BAA2B,CAAC,IAAI,MAAM,CAAA;QAC7G,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,uBAAuB,CAAC,IAAI,MAAM,CAAA;QACjG,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,YAAY,EAAE,wBAAwB,CAAC,IAAI,MAAM,CAAA;QACpG,WAAW,CAAC,SAAS,GAAI,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,WAAW,EAAE,uBAAuB,CAAC,IAAI,OAAO,CAAA;QACnG,OAAO,WAAW,CAAC;IACrB,CAAC;IAED,OAAO,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM;QACjE,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,EAAE;YACzB,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SAC3B;QACD,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IACnB,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE,MAAM;QACvB,MAAM,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IACvB,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACtD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAA;IAC/B,CAAC;IAED,qBAAqB,IAAI,IAAI;QAC3B,IAAI,CAAC,cAAc,GAAG,SAAS,CAAA;QAC/B,IAAI,CAAC,kBAAkB,EAAE,CAAA;IAC3B,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClC,IAAG,IAAI,CAAC,qBAAqB,EAAC;YAC5B,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,IAAI,OAAO,GAAG,IAAI,CAAC,cAAc,CAAC,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC;QACxD,IAAI,IAAI,EAAE,gBAAgB,CAAC,aAAa,GAAG,OAAO,CAAC;QACnD,IAAI,CAAC,OAAO,GAAG,MAAM,gBAAgB,CAAC,eAAe,CAAC,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;QACrF,0BAA0B;QAC1B,MAAM,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC;QAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qCAAqC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAE3E,IAAI,aAAa,EAAE,SAAS,CAAC,aAAa,GAAG;YAC3C,KAAK,EAAE;gBACL;oBACE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,UAAU;oBAC3C,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC,IAAI;iBACvC;aACF;YACD,UAAU,EAAE,SAAS,CAAC,aAAa,CAAC,aAAa;YACjD,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC;SAC/D,CAAC;QAEF,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;YACrE,qBAAqB,CAAC,sBAAsB,CAAC,OAAO,EAAE,qBAAqB,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACjI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mDAAmD,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;gBAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,uDAAuD,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QACjC,IAAG,CAAC,IAAI,CAAC,qBAAqB,EAAC;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,MAAM,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;QAC9B,qBAAqB,CAAC,qBAAqB,CAAC,IAAI,CAAC,cAAc,EAAE,UAAU,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YAC/F,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,kDAAkD,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sDAAsD,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;IACL,CAAC;CACF;AAED,MAAM,OAAO,YAAa,YAAW,aAAa;IAChD,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,GAAG,SAAS,CAAC;IAChD,OAAO,CAAC,SAAS,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI,CAAA;IAE1C,YAAY,YAAY,EAAE,YAAY;QACpC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAA;IAC1C,CAAC;IAED,QAAQ,CAAC,IAAI,KAAU,EAAE,MAAM,EAAE,SAAS,GAAG,IAAI;QAC/C,IAAI,CAAC,SAAS,GAAG,MAAM,CAAA;IACzB,CAAC;IAED,QAAQ,CAAC,IAAI,KAAU,GAAG,IAAI;QAC5B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;IACvB,CAAC;IAED,OAAO,CAAC,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,CAAC,MAAM,QAAa;QAC/E,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,MAAM,CAAC,CAAA;QACzB,IAAI,CAAC,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,KAAU;QACtE,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;IAC9D,CAAC;IAED,OAAO;QACL,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;SACrB;QACD,IAAI,CAAC,YAAY,EAAE,gBAAgB,CAAC,IAAI,CAAC,CAAA;IAC3C,CAAC;CACF;AAED,UAAU,eAAe;IACvB,WAAW,IAAI,IAAI,CAAA;IAEnB,YAAY,IAAI,IAAI,CAAA;CACrB", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/player/FocusManager.ts": {"version": 3, "file": "FocusManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/player/FocusManager.ets"], "names": [], "mappings": "YAeO,aAAa,MAAM,iBAAiB;AAE3C,MAAM,CAAC,OAAO,OAAO,YAAY;IAC/B,OAAO,CAAC,MAAM,EAAE,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IAE5C,YAAY,MAAM,EAAE,aAAa;QAC/B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,UAAU;IAEV,CAAC;IAED,sBAAsB,CAAC,OAAO,EAAE,QAAQ;QACtC,OAAO,EAAE,CAAA;IACX,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/player/MediaPlayerPlayer.ts": {"version": 3, "file": "MediaPlayerPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/player/MediaPlayerPlayer.ets"], "names": [], "mappings": "YAeO,gBAAgB,MAAM,qBAAqB;YAC3C,MAAM,MAAM,kBAAkB;YAC9B,MAAM,MAAM,UAAU;YACtB,aAAa,MAAM,iBAAiB;OACpC,KAAK;cACH,aAAa;OACf,EAAE,GAAG,EAAE;OACL,MAAM;YACR,MAAM;AAEb,MAAM,GAAG,GAAG,mBAAmB,CAAA;AAC/B,MAAM,aAAa,EAAE,MAAM,GAAG,GAAG,CAAC;AAClC,MAAM,oBAAoB,EAAE,MAAM,GAAG,OAAO,CAAC;AAC7C,MAAM,iBAAiB,EAAE,MAAM,GAAG,OAAO,CAAC;AAE1C,MAAM,CAAC,OAAO,OAAO,iBAAkB,YAAW,MAAM;IACtD,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC;IACrC,OAAO,CAAC,WAAW,CAAC,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;IACnD,OAAO,CAAC,SAAS,EAAE,OAAO,GAAG,KAAK,CAAC;IACnC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;IAC3B,OAAO,CAAC,WAAW,EAAE,OAAO,GAAG,KAAK,CAAC;IACrC,OAAO,CAAC,SAAS,EAAE,OAAO,GAAG,KAAK,CAAC;IACnC,OAAO,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;IAEzD,YAAY,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB;QACjE,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;IACrC,CAAC;IAED,KAAK,CAAC,eAAe;QACnB,IAAI,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;QAChD,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;QAC1C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;IAChC,CAAC;IAED,mBAAmB,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ;QAC1C,eAAe;QACf,QAAQ,CAAC,EAAE,CAAC,UAAU,EAAE,CAAC,YAAY,EAAE,MAAM,EAAE,EAAE;YAC/C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,yCAAyC,YAAY,EAAE,CAAC,CAAC;YACpE,IAAI,CAAC,aAAa,CAAC,cAAc,EAAE,CAAA;QACrC,CAAC,CAAC,CAAA;QAEF,QAAQ,CAAC,EAAE,CAAC,iBAAiB,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC,iBAAiB,EAAE,KAAK,EAAE,MAAM,EAAE,EAAE;YAClF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qCAAqC,KAAK,EAAE,CAAC,CAAC;YACzD,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QACvC,CAAC,CAAC,CAAA;QACF,QAAQ,CAAC,EAAE,CAAC,WAAW,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;YACzC,OAAO,CAAC,IAAI,CAAC,sCAAsC,GAAG,KAAK,CAAC,CAAA;QAC9D,CAAC,CAAC,CAAA;QACF,mDAAmD;QACnD,QAAQ,CAAC,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YAC1C,IAAI,GAAG,CAAC,IAAI,IAAI,aAAa,IAAI,GAAG,CAAC,IAAI,IAAI,oBAAoB,IAAI,GAAG,CAAC,IAAI,IAAI,iBAAiB,EAAE;gBAClG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,kCAAkC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gBACrE,OAAO;aACR;YACD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mCAAmC,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YACrF,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO,CAAC,CAAA;QACnD,CAAC,CAAC,CAAA;QACF,YAAY;QACZ,QAAQ,CAAC,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,iBAAiB,EAAE,EAAE;YAClF,QAAQ,KAAK,EAAE;gBACb,KAAK,MAAM,EAAE,uBAAuB;oBAClC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,6BAA6B,CAAC,CAAC;oBAC1C,MAAM;gBACR,KAAK,aAAa,EAAE,yBAAyB;oBAC3C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oCAAoC,CAAC,CAAC;oBACjD,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAA;oBACpE,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnC,MAAM;gBACR,KAAK,UAAU,EAAE,qBAAqB;oBACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC;oBAC9C,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;oBACxC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBAC/B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACzB,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC;oBAChC,MAAM;gBACR,KAAK,SAAS,EAAE,oBAAoB;oBAClC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC;oBAC7C,IAAI,CAAC,aAAa,CAAC,mBAAmB,EAAE,CAAC;oBACzC,MAAM;gBACR,KAAK,QAAQ,EAAE,qBAAqB;oBAClC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,+BAA+B,CAAC,CAAC;oBAC5C,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;oBACxC,MAAM;gBACR,KAAK,WAAW,EAAE,gBAAgB;oBAChC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,kCAAkC,CAAC,CAAC;oBAC/C,IAAI,CAAC,aAAa,CAAC,YAAY,EAAE,CAAA;oBACjC,MAAM;gBACR,KAAK,SAAS,EAAE,sBAAsB;oBACpC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC;oBAC7C,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;oBACxC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;oBACnC,MAAM;gBACR,KAAK,UAAU;oBACb,IAAI,CAAC,aAAa,CAAC,kBAAkB,EAAE,CAAC;oBACxC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iCAAiC,CAAC,CAAC;oBAC9C,MAAM;gBACR;oBACE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC;oBAC7C,MAAM;aACT;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,WAAW,IAAI,MAAM;QACnB,qDAAqD;QACrD,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC3D,CAAC;IAED,kBAAkB,IAAI,MAAM;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC9D,CAAC;IAED,iBAAiB,IAAI,OAAO;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,SAAS,CAAC,CAAC,CAAC,KAAK,CAAA;IACxE,CAAC;IAED,YAAY,IAAI,OAAO;QACrB,IAAI,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;QAClC,OAAO,QAAQ,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC,CAAC,CAAA;IACxC,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,SAAS,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;IAC7F,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QACzB,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;IACpD,CAAC;IAED,MAAM,CAAC,QAAQ,EAAE,MAAM;QACrB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;YAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAA;SACjC;IACH,CAAC;IAED,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM;QAC/C,IAAI,CAAC,MAAM,GAAG,UAAU,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC;SACzC;IACH,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,MAAM;QAClB,IAAI,IAAI,GAAG,CAAC,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,sBAAsB,GAAG,IAAI,CAAC,CAAC;SAChD;QACD,IAAG,IAAI,IAAI,CAAC,EAAC;YACX,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;YAC1B,OAAO;SACR;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;QACtD,IAAI,IAAI,IAAI,GAAG,EAAE;YACf,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACvD;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACvD;aAAM,IAAI,IAAI,IAAI,GAAG,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACvD;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACvD;aAAM,IAAI,IAAI,IAAI,GAAG,EAAE;YACtB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACvD;aAAM,IAAI,IAAI,IAAI,IAAI,EAAE;YACvB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACvD;aAAM;YACL,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAA;SACtD;QACD,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1D,IAAG,IAAI,CAAC,WAAW,EAAE,KAAK,IAAI,QAAQ,EAAC;YACrC,IAAI,CAAC,KAAK,EAAE,CAAC;SACd;IACH,CAAC;IAED,UAAU,CAAC,OAAO,EAAE,OAAO;QACzB,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC;QACzB,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;YACvB,IAAI,CAAC,WAAW,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC;SAClC;IACH,CAAC;IAED,OAAO,CAAC,YAAY;QAClB,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,SAAS;eACpG,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,QAAQ,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,WAAW,CAAC,CAAA;IACrF,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE,gBAAgB;QAC3C,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,aAAa,IAAI,OAAO,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAChH,IAAI,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;YACvC,IAAI,SAAS,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,MAAM,CAAC,gBAAgB,CAAA;YAC3D,IAAI,WAAW,EAAE,MAAM,CAAC,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,CAAA;YACtE,WAAW,CAAC,qBAAqB,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;YACrD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAA;SACnC;IACH,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM;QAC5B,MAAM,IAAI,CAAC,KAAK,EAAE,CAAA;QAClB,IAAI,CAAC,WAAW,IAAI,MAAM,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,IAAI,CAAC,UAAU,EAAE,EAAE;YACrB,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,CAAA;SAClC;aAAM;YACL,IAAI,CAAC,WAAW,GAAG,IAAI,CAAA;SACxB;IACH,CAAC;IAED,OAAO,CAAC,UAAU;QAChB,OAAO,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,SAAS,IAAI,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI,aAAa,CAAC,CAAA;IAC/G,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,CAAC,WAAW,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAA;IACpD,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/player/Player.ts": {"version": 3, "file": "Player.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/player/Player.ets"], "names": [], "mappings": "YAeO,gBAAgB,MAAM,qBAAqB;YAC3C,MAAM,MAAM,kBAAkB;AAErC,MAAM,CAAC,OAAO,WAAW,MAAM;IAC7B,WAAW,IAAI,MAAM,GAAG,IAAI,CAAC;IAE7B,kBAAkB,IAAI,MAAM,GAAG,IAAI,CAAC;IAEpC,iBAAiB,IAAI,OAAO,CAAC;IAE7B,YAAY,IAAI,OAAO,CAAC;IAExB,KAAK,IAAI,IAAI,CAAC;IAEd,KAAK,IAAI,IAAI,CAAC;IAEd,IAAI,IAAI,IAAI,CAAC;IAEb,MAAM,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAE/B,OAAO,IAAI,IAAI,CAAC;IAEhB,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAEzD,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC;IAE5B,UAAU,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAEnC,aAAa,CAAC,OAAO,EAAE,gBAAgB,GAAG,IAAI,CAAC;IAE/C,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAEhC,OAAO,IAAI,IAAI,CAAC;IAEhB,KAAK,IAAI,IAAI,CAAC;CACf", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/player/SoundPoolPlayer.ts": {"version": 3, "file": "SoundPoolPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/player/SoundPoolPlayer.ets"], "names": [], "mappings": "OAeO,KAAK;OACL,KAAK;YACL,gBAAgB,MAAM,qBAAqB;YAC3C,kBAAkB,MAAM,uBAAuB;YAC/C,MAAM,MAAM,kBAAkB;YAC9B,SAAS,MAAM,qBAAqB;YACpC,MAAM,MAAM,UAAU;YACtB,aAAa,MAAM,iBAAiB;OAClC,OAAO;AAEhB,MAAM,WAAW,GAAG,EAAE,CAAC;AAEvB,MAAM,CAAC,OAAO,OAAO,eAAgB,YAAW,MAAM;IACpD,aAAa,EAAE,aAAa,CAAC;IAC7B,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IAC3C,OAAO,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAC9B,OAAO,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IACvC,OAAO,CAAC,YAAY,EAAE,gBAAgB,CAAC;IACvC,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAC;IACzD,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IAEjD,YAAY,aAAa,EAAE,aAAa,EAAE,gBAAgB,EAAE,gBAAgB;QAC1E,IAAI,CAAC,aAAa,GAAG,aAAa,CAAC;QACnC,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,aAAa,CAAC,OAAO,CAAC;IAC5C,CAAC;IAED,KAAK,CAAC,IAAI;QACR,MAAM,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,WAAW,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;QACnF,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrF,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,SAAS,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,YAAY,EAAE,gBAAgB;QAClD,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE;YACtC,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;YACjC,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;YACrB,MAAM,IAAI,CAAC,IAAI,EAAE,CAAA;SAClB;IACH,CAAC;IAED,WAAW,IAAI,MAAM,GAAG,IAAI;QAC1B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kBAAkB,IAAI,MAAM,GAAG,IAAI;QACjC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,iBAAiB,IAAI,OAAO;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,YAAY,IAAI,OAAO;QACrB,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAA;QAC5B,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;QAC1B,IAAI,OAAO,IAAI,IAAI,EAAE;YACnB,IAAI,cAAc,EAAE,KAAK,CAAC,cAAc,GAAG;gBACzC,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;gBAC9C,IAAI,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,CAAC;gBACpD,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;gBAC1C,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE;gBAC3C,QAAQ,EAAE,CAAC,EAAE,QAAQ;aACtB,CAAA;YACD,IAAI,CAAC,QAAQ,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC,CAAA;SAC1E;IACH,CAAC;IAED,KAAK;QACH,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAED,IAAI;QACF,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;YACnC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,EAAE,MAAM;QACrB,MAAM;QACN,mCAAmC;IACrC,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,CAAC,IAAI,EAAE,CAAA;QACjB,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,OAAM;SACP;QACD,IAAI,IAAI,CAAC,YAAY,EAAE,IAAI,IAAI,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,EAAE;YACpE,OAAO;SACR;QACD,IAAI,iBAAiB,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAC;QACrF,IAAI,iBAAiB,CAAC,MAAM,IAAI,CAAC,IAAI,iBAAiB,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;YACjE,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,YAAY,EAAE,CAAC,CAAA;YAC/D,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC1C,IAAI,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC3D,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,2BAA2B,CAAC,CAAA;SAC1D;aAAM;YACL,+EAA+E;YAC/E,IAAI,KAAK,GAAG,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YAC5C,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;gBACd,iBAAiB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;aACnC;SACF;QACD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;IACrB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM;QACrD,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,CAAC,CAAA;SACxE;IACH,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM;QACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,CAAA;SACrE;IACH,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE,OAAO;QAC/B,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACzB,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SAChE;IACH,CAAC;IAED,aAAa,CAAC,OAAO,EAAE,gBAAgB;QACrC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAA;IAC7B,CAAC;IAED,SAAS,CAAC,MAAM,EAAE,MAAM;QACtB,MAAM,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;IAC9B,CAAC;IAED,OAAO;QACL,2DAA2D;IAC7D,CAAC;IAED,KAAK;QACH,iCAAiC;IACnC,CAAC;IAED,OAAO,CAAC,oBAAoB,CAAC,OAAO,EAAE,MAAM;QAC1C,MAAM,IAAI,KAAK,CAAC,sCAAsC,OAAO,EAAE,CAAC,CAAA;IAClE,CAAC;IAED,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,iBAAiB;QACxD,IAAI,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QACxD,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;YACpC,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,gBAAgB,CAAC;SACnD;aAAM,IAAI,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,GAAG,CAAC,EAAE;YAC3C,MAAM,GAAG,KAAK,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;SACrD;QACD,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,SAAS,EAAE,SAAS;QACrC,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;SACrB;QACD,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE;YAC1D,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,EAAE,KAAK,CAAC,eAAe,GAAG,CAAC,CAAA;SAC7E;QACD,IAAI,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpE,IAAI,cAAc,GAAG,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAClE,IAAI,cAAc,IAAI,IAAI,EAAE;YAC1B,qDAAqD;YACrD,IAAI,QAAQ,GAAG,cAAc,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC;YAC1D,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;YACzC,IAAI,CAAC,OAAO,GAAG,cAAc,CAAC,OAAO,CAAA;YACrC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,mBAAmB,IAAI,CAAC,OAAO,QAAQ,SAAS,CAAC,GAAG,gBAAgB,QAAQ,EAAE,CAAC,CAAA;SAC7G;aAAM;YACL,oCAAoC;YACpC,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACtC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,2BAA2B,SAAS,CAAC,GAAG,GAAG,CAAC,CAAA;YACzE,IAAI,SAAS,GAAG,MAAM,SAAS,CAAC,wBAAwB,CAAC,IAAI,CAAC,aAAa,CAAC,qBAAqB,EAAE,CAAC,CAAC;YACrG,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,eAAe,SAAS,EAAE,CAAC,CAAA;YACxD,IAAI,UAAU,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YACtD,IAAI,CAAC,gBAAgB,CAAC,CAAC,eAAe,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;YAC5D,IAAI,CAAC,OAAO,GAAG,UAAU,CAAA;YACzB,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,2BAA2B,SAAS,CAAC,GAAG,KAAK,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK,WAAW,IAAI,GAAG,CAAE,CAAA;SAC3H;QACD,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACxB,CAAC;IAED,YAAY,IAAI,SAAS,GAAG,IAAI;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,CAAC;QAC5C,OAAO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,EAAE,IAAI,SAAS,CAAC,CAAC,CAAC,IAAI,CAAA;IACpE,CAAC;CACF;AAED,MAAM,OAAO,gBAAgB;IAC3B,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC;IAChC,OAAO,CAAC,sBAAsB,EAAE,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAA;IAC9D,OAAO,CAAC,iBAAiB,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,gBAAgB,GAAG,CAAA;IAEnE,YAAY,MAAM,EAAE,kBAAkB;QACpC,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,gBAAgB;QAC7E,IAAI,KAAK,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC;QACjD,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,EAAE;YACvC,IAAI,SAAS,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC,UAAU,EAAE,KAAK,CAAC,CAAC;YAC/D,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,wBAAwB,GAAG,GAAG,CAAC,CAAC;YACzD,IAAI,gBAAgB,GAAG,IAAI,gBAAgB,CAAC,SAAS,CAAC,CAAC;YACvD,gBAAgB,CAAC,SAAS,CAAC,EAAE,CAAC,cAAc,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAChE,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,UAAU,OAAO,EAAE,CAAC,CAAA;gBAC7C,IAAI,aAAa,GAAG,gBAAgB,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;gBACjE,IAAI,SAAS,GAAG,aAAa,CAAC,YAAY,EAAE,CAAA;gBAC5C,IAAI,SAAS,EAAE;oBACb,gBAAgB,CAAC,eAAe,CAAC,MAAM,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;oBAC/D,IAAI,UAAU,GAAG,gBAAgB,CAAC,YAAY,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;oBAC7D,KAAK,IAAI,MAAM,IAAI,UAAU,EAAE;wBAC7B,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,WAAW,MAAM,CAAC,OAAO,YAAY,CAAC,CAAC;wBACtE,MAAM,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;wBACvC,IAAI,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE,EAAE;4BACpC,MAAM,CAAC,aAAa,CAAC,SAAS,CAAC,oBAAoB,MAAM,CAAC,OAAO,EAAE,CAAC,CAAC;4BACrE,MAAM,CAAC,KAAK,EAAE,CAAC;yBAChB;qBACF;iBACF;YACH,CAAC,CAAC,CAAC;YACH,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,EAAE,gBAAgB,CAAC,CAAC;SACnD;IACH,CAAC;IAED;;OAEG;IACH,mBAAmB,CAAC,YAAY,EAAE,gBAAgB,GAAG,gBAAgB,GAAG,IAAI;QAC1E,IAAI,KAAK,GAAG,YAAY,CAAC,eAAe,EAAE,CAAC;QAC3C,IAAI,GAAG,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC;QACjD,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IACrF,CAAC;IAED,OAAO;QACL,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,gBAAgB,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAA;QACzF,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE,CAAA;IAChC,CAAC;CACF;AAED,MAAM,gBAAgB;IACpB,SAAS,EAAE,KAAK,CAAC,SAAS,CAAC;IAC3B,eAAe,GAAG,IAAI,OAAO,CAAC,MAAM,EAAE,eAAe,GAAG,CAAA;IACxD,YAAY,GAAG,IAAK,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,eAAe,CAAC,GAAG,CAAA;IAEhE,YAAY,SAAS,EAAE,KAAK,CAAC,SAAS;QACpC,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED;;OAEG;IAEH;;;OAGG;IACH,OAAO;QACL,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACnC,IAAI,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC;QACzB,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC;QAC7B,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/player/WrappedPlayer.ts": {"version": 3, "file": "WrappedPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/player/WrappedPlayer.ets"], "names": [], "mappings": "YAeO,gBAAgB,MAAM,qBAAqB;YAC3C,kBAAkB,MAAwB,uBAAuB;cAA3C,YAAY,QAAQ,uBAAuB;OACjE,EAAE,WAAW,EAAE;OACf,EAAE,UAAU,EAAE;YACd,MAAM,MAAM,kBAAkB;OAC9B,YAAY;YACZ,MAAM,MAAM,UAAU;cACpB,OAAO,IAAP,OAAO;YACT,KAAK;OACL,eAAqC;cAAlB,gBAAgB;OACnC,iBAAiB;AAExB,MAAM,CAAC,OAAO,OAAO,aAAa;IAChC,OAAO,CAAC,GAAG,EAAE,kBAAkB,CAAC;IAChC,YAAY,EAAE,YAAY,CAAC;IAC3B,OAAO,EAAE,gBAAgB,CAAC;IAC1B,OAAO,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;IAC3C,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IACrC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IACrC,OAAO,CAAC,QAAQ,EAAE,OAAO,GAAG,IAAI,CAAC;IACjC,OAAO,CAAC,QAAQ,EAAE,OAAO,GAAG,KAAK,CAAC;IAClC,OAAO,CAAC,OAAO,EAAE,OAAO,GAAG,KAAK,CAAC;IACjC,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,GAAG,CAAC;IAC7B,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,GAAG,CAAC;IAC9B,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,GAAG,CAAC;IAC3B,OAAO,CAAC,WAAW,EAAE,WAAW,GAAG,WAAW,CAAC,OAAO,CAAC;IACvD,OAAO,CAAC,SAAS,EAAE,OAAO,GAAG,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC;IAClE,OAAO,CAAC,UAAU,EAAE,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC;IACzD,OAAO,CAAC,YAAY,EAAE,MAAM,GAAG,CAAC,CAAC,CAAA;IACjC,OAAO,CAAC,YAAY,GAAG,IAAI,YAAY,CAAC,IAAI,CAAC,CAAA;IAE7C,YAAY,MAAM,EAAE,kBAAkB,EAAE,YAAY,EAAE,YAAY,EAAE,OAAO,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,gBAAgB;QAC/H,IAAI,CAAC,GAAG,GAAG,MAAM,CAAC;QAClB,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;QACjC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC3C,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;QAClC,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;YACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YACpB,IAAI,KAAK,IAAI,IAAI,EAAE;gBACjB,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,iBAAiB,EAAE,CAAA;gBAC3C,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;gBACvB,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;aAC9B;iBAAM;gBACL,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;gBACpB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;gBACvB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;gBACpB,MAAM,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAA;aAC7B;SACF;aAAM;YACL,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,CAAA;SACpC;IACH,CAAC;IAED,SAAS,IAAI,MAAM,GAAG,IAAI;QACxB,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,SAAS,IAAK,OAAO;QACnB,OAAO,IAAI,CAAC,OAAO,CAAA;IACrB,CAAC;IAED,SAAS,CAAC,KAAK,EAAE,MAAM;QACrB,IAAI,IAAI,CAAC,MAAM,IAAI,KAAK,EAAE;YACxB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;YACnB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,mBAAmB,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;aAC9C;SACF;IACH,CAAC;IAED,UAAU,CAAC,KAAK,EAAE,MAAM;QACtB,IAAI,IAAI,CAAC,OAAO,IAAI,KAAK,EAAE;YACzB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;YACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,CAAA;aAC7C;SACF;IACH,CAAC;IAED,OAAO,CAAC,KAAK,EAAE,MAAM;QACnB,IAAI,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE;YACtB,IAAI,CAAC,IAAI,GAAG,KAAK,CAAA;YACjB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,KAAK,CAAC,CAAA;SAC3C;IACH,CAAC;IAED,cAAc,CAAC,KAAK,EAAE,WAAW;QAC/B,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,EAAE;YAC7B,IAAI,CAAC,WAAW,GAAG,KAAK,CAAA;YACxB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC;YACtD,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAClB,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;aACxC;SACF;IACH,CAAC;IAED,UAAU;QACR,OAAO,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,UAAU;QACnC,IAAI,IAAI,CAAC,UAAU,IAAI,KAAK,EAAE;YAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;YACvB,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,uBAAuB,EAAE,CAAA;gBAClD,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;gBACvB,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,CAAA;aAC5B;YACD,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;SACxB;IACH,CAAC;IAED,WAAW,CAAC,KAAK,EAAE,OAAO;QACxB,IAAI,IAAI,CAAC,QAAQ,IAAI,KAAK,EAAE;YAC1B,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YACrB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAA;SACrC;IACH,CAAC;IAED,WAAW,IAAI,OAAO;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAA;IACtB,CAAC;IAED,SAAS;QACP,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,OAAO;QACL,OAAO,IAAI,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,OAAO,CAAC,uBAAuB,IAAI,MAAM;QACvC,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAC;YAChD,OAAO,QAAQ,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACzC;QACD,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,iBAAiB;QAC7B,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;QAChC,IAAI,IAAI,CAAC,QAAQ,IAAI,aAAa,IAAI,IAAI,EAAE;YAC1C,IAAI,CAAC,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC;YACxC,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAA;YACrB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAA;SACpB;aAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YACxB,MAAM,aAAa,EAAE,KAAK,EAAE,CAAA;YAC5B,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;SACzB;QACD,OAAO,aAAa,CAAC,CAAA;IACvB,CAAC;IAED,KAAK,CAAC,kBAAkB,CAAC,YAAY,EAAE,gBAAgB;QACrD,IAAI,IAAI,CAAC,OAAO,IAAI,YAAY,EAAE;YAChC,OAAM;SACP;QACD,kBAAkB;QAClB,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC,IAAI,EAAE,CAAA;QAElC,uCAAuC;QACvC,uDAAuD;QACvD,0DAA0D;QAE1D,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAA;YACxB,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,CAAA;YACzB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;YACvB,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YACvC,IAAI,IAAI,CAAC,MAAM,EAAE;gBACf,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;gBAClC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;aAC9B;SACF;IACH,CAAC;IAED,WAAW,IAAI,MAAM,GAAG,IAAI;QAC1B,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IAC5E,CAAC;IAED,kBAAkB,IAAI,MAAM,GAAG,IAAI;QACjC,OAAO,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,kBAAkB,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACnF,CAAC;IAED,iBAAiB,IAAI,OAAO;QAC1B,OAAO,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI,IAAI,CAAA;IACzG,CAAC;IAED,qBAAqB,IAAI,OAAO;QAC9B,OAAO,IAAI,CAAC,GAAG,CAAC,qBAAqB,EAAE,CAAA;IACzC,CAAC;IAED,eAAe,IAAI,KAAK,CAAC,YAAY;QACnC,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA;IACnC,CAAC;IAED,IAAI;QACF,IAAI,CAAC,YAAY,CAAC,sBAAsB,CAAC,KAAK,IAAI,EAAE,CAAC,MAAM,IAAI,CAAC,YAAY,EAAE,CAAC,CAAA;IACjF,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YACnC,IAAI,aAAa,GAAG,IAAI,CAAC,MAAM,CAAC;YAChC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAA;YACnB,IAAI,aAAa,IAAI,IAAI,EAAE;gBACzB,MAAM,IAAI,CAAC,UAAU,EAAE,CAAA;aACxB;iBAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACxB,MAAM,aAAa,CAAC,KAAK,EAAE,CAAA;gBAC3B,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA;aAC3B;SACF;IACH,CAAC;IAED,KAAK,CAAC,IAAI;QACR,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAM;SACP;QACD,IAAI,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,OAAO,EAAE;YAC3C,MAAM,IAAI,CAAC,KAAK,EAAE,CAAA;YAClB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE;oBACvC,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAA;oBACzB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;oBACvB,MAAM,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAA;iBAC7B;qBAAM;oBACL,6EAA6E;oBAC7E,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;iBACb;aACF;SACF;aAAM;YACL,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;SACrB;IACH,CAAC;IAED,KAAK,CAAC,OAAO;QACX,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAA;QAC9B,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAM;SACP;QACD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAA;SAC1B;QAED,mEAAmE;QACnE,kCAAkC;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAA;QACpB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;IACpB,CAAC;IAED,KAAK,CAAC,KAAK;QACT,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAA;YACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAA;aAC3B;SACF;IACH,CAAC;IAED,+CAA+C;IAC/C,uBAAuB;IACvB,IAAI,CAAC,QAAQ,EAAE,MAAM;QACnB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE;YACxD,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,QAAQ,CAAC,CAAA;YAC7B,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAA;SACvB;aAAM;YACL,IAAI,CAAC,YAAY,GAAG,QAAQ,CAAA;SAC7B;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,UAAU;QACd,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAA;QACtB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;QAC7B,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,MAAM,IAAI,CAAC,MAAM,EAAE,KAAK,EAAE,CAAA;YAC1B,IAAI,CAAC,GAAG,CAAC,eAAe,EAAE,CAAA;SAC3B;QACD,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,IAAI,IAAI,EAAE;YACjE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,IAAI,CAAC,YAAY,CAAC,CAAA;SACvC;IACH,CAAC;IAED,KAAK,CAAC,YAAY;QAChB,IAAI,IAAI,CAAC,WAAW,IAAI,WAAW,CAAC,IAAI,EAAE;YACxC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAA;SAClB;QACD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAA;IAC/B,CAAC;IAED,WAAW,CAAC,OAAO,EAAE,MAAM;QACzB,sCAAsC;IACxC,CAAC;IAED,cAAc;QACZ,IAAI,CAAC,GAAG,CAAC,kBAAkB,CAAC,IAAI,CAAC,CAAA;IACnC,CAAC;IAED,SAAS,CAAC,OAAO,EAAE,MAAM;QACvB,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,EAAE,OAAO,CAAC,CAAA;IACnC,CAAC;IAED,WAAW,CAAC,SAAS,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,EAAE,MAAM,EAAE,YAAY,CAAC,KAAU;QAC5E,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,EAAE,YAAY,EAAE,YAAY,CAAC,CAAA;IACnE,CAAC;IAED,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,GAAG,OAAO;QAC3C,uBAAuB;QACvB,wDAAwD;QACxD,6EAA6E;QAC7E,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;QACvB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,CAAA;QAC9C,OAAO,KAAK,CAAA;IACd,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,MAAM,CAAC;QAC3C,IAAI,IAAI,CAAC,UAAU,IAAI,UAAU,CAAC,WAAW,EAAE;YAC7C,IAAI,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YAC9D,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;YACpB,OAAO,MAAM,CAAC;SACf;aAAM;YACL,IAAI,MAAM,GAAG,IAAI,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YACvD,MAAM,MAAM,CAAC,eAAe,EAAE,CAAA;YAC9B,OAAO,MAAM,CAAA;SACd;IACH,CAAC;IAED;;OAEG;IACH,OAAO,CAAC,KAAK,CAAC,UAAU;QACtB,IAAI,MAAM,GAAG,MAAM,IAAI,CAAC,YAAY,EAAE,CAAA;QACtC,mGAAmG;QACnG,IAAI,CAAC,MAAM,GAAG,MAAM,CAAA;QACpB,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,MAAM,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA;YACxC,MAAM,IAAI,CAAC,gBAAgB,EAAE,CAAA;SAC9B;IACH,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;QAC/B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;QACnD,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;QACvC,MAAM,IAAI,CAAC,MAAM,EAAE,OAAO,EAAE,CAAA;IAC9B,CAAC;IAED,OAAO,CAAC,mBAAmB,CAAC,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM;QACzD,IAAI,UAAU,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,MAAM,CAAA;QAClD,IAAI,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,GAAG,MAAM,CAAA;QACnD,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;IACjD,CAAC;IAED,KAAK,CAAC,OAAO;QACX,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;QACpB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,CAAA;IAC7B,CAAC;IAED,mBAAmB;QACjB,IAAI,CAAC,GAAG,CAAC,mBAAmB,EAAE,CAAA;IAChC,CAAC;IAED,kBAAkB;QAChB,IAAI,CAAC,GAAG,CAAC,kBAAkB,EAAE,CAAA;IAC/B,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/PlayerMode.ts": {"version": 3, "file": "PlayerMode.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/PlayerMode.ets"], "names": [], "mappings": "AACA;;;;;;;;;;;;;GAaG;AAEF,MAAM,MAAM,UAAU;IACrB,YAAY,IAAI;IAChB,WAAW,IAAI;CAChB;AAED,MAAM,CAAC,MAAM,eAAe,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;IAC9C,IAAI,IAAI,IAAI,uBAAuB,EAAE;QACnC,OAAO,UAAU,CAAC,WAAW,CAAA;KAC9B;IACD,OAAO,UAAU,CAAC,YAAY,CAAC;AACjC,CAAC,CAAA", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/ReleaseMode.ts": {"version": 3, "file": "ReleaseMode.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/ReleaseMode.ets"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,MAAM,MAAM,WAAW;IACrB,OAAO,IAAI;IACX,IAAI,IAAI;IACR,IAAI,IAAI;CACT;AAED,MAAM,CAAC,MAAM,gBAAgB,GAAG,CAAC,IAAI,EAAE,MAAM,EAAE,EAAE;IAC/C,IAAI,IAAI,IAAI,kBAAkB,EAAE;QAC9B,OAAO,WAAW,CAAC,IAAI,CAAA;KACxB;SAAM,IAAI,IAAI,IAAI,kBAAkB,EAAE;QACrC,OAAO,WAAW,CAAC,IAAI,CAAA;KACxB;IACD,OAAO,WAAW,CAAC,OAAO,CAAC;AAC7B,CAAC,CAAA", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/source/BytesSource.ts": {"version": 3, "file": "BytesSource.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/source/BytesSource.ets"], "names": [], "mappings": "YAeO,eAAe,MAAM,2BAA2B;YAChD,MAAM,MAAM,UAAU;YACtB,KAAK;AAEZ,MAAM,CAAC,OAAO,OAAO,WAAY,YAAW,MAAM;IAChD,OAAO,CAAC,KAAK,EAAE,WAAW,CAAA;IAE1B,YAAY,KAAK,EAAE,WAAW;QAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IACrB,CAAC;IAED,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI;QAClD,IAAI,GAAG,EAAE,KAAK,CAAC,mBAAmB,GAAG;YACnC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,UAAU;YAC/B,QAAQ,EAAE,CAAC,GAAG,EAAE,WAAW,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,SAAS,EAAE,EAAE;gBACtE,IAAI,GAAG,IAAI,SAAS,IAAI,MAAM,IAAI,SAAS,IAAI,GAAG,IAAI,SAAS,EAAE;oBAC/D,OAAO,CAAC,CAAC,CAAC;iBACX;gBACD,IAAI,GAAG,IAAI,GAAG,CAAC,QAAQ,EAAE;oBACvB,OAAO,CAAC,CAAC,CAAA;iBACV;gBACD,IAAI,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;gBAC3D,IAAI,aAAa,GAAG,CAAC,EAAE;oBACrB,OAAO,CAAC,CAAC,CAAC;iBACX;gBACD,MAAM,IAAI,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;gBACjC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,GAAG,GAAG,aAAa,CAAC,CAAC;gBAC9D,MAAM,QAAQ,GAAG,IAAI,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC5C,mBAAmB;gBACnB,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAEnB,IAAI,aAAa,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,GAAG,CAAC,EAAE;oBAC9C,OAAO,aAAa,CAAC;iBACtB;gBACD,OAAO,CAAC,CAAC,CAAC;YACZ,CAAC;SACF,CAAA;QACD,WAAW,CAAC,OAAO,GAAG,GAAG,CAAA;IAC3B,CAAC;IAED,eAAe,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI;QACrD,MAAM,IAAI,KAAK,CAAC,0DAA0D,CAAC,CAAA;IAC7E,CAAC;IAED,OAAO,CAAC,oBAAoB,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,MAAM;QACpE,IAAI,aAAa,GAAG,MAAM,CAAA;QAC1B,IAAI,QAAQ,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,EAAE;YACpD,aAAa,IAAI,QAAQ,GAAG,aAAa,GAAG,IAAI,CAAC,KAAK,CAAC,UAAU,CAAA;SAClE;QACD,OAAO,aAAa,CAAA;IACtB,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/source/Source.ts": {"version": 3, "file": "Source.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/source/Source.ets"], "names": [], "mappings": "YAeO,KAAK;YACL,eAAe,MAAM,2BAA2B;AAEvD,MAAM,CAAC,OAAO,WAAW,MAAM;IAC7B,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,CAAC;IAErD,eAAe,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI,CAAC;CACzD", "entry-package-info": "audioplayers_ohos|1.0.0"}, "audioplayers_ohos|audioplayers_ohos|1.0.0|src/main/ets/components/plugin/source/UrlSource.ts": {"version": 3, "file": "UrlSource.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/source/UrlSource.ets"], "names": [], "mappings": "YAeO,eAAe,MAAM,2BAA2B;YAChD,MAAM,MAAM,UAAU;YACtB,KAAK;OACL,EAAE;OACF,OAAO;AAEd,MAAM,CAAC,OAAO,OAAO,SAAU,YAAW,MAAM;IAC9C,GAAG,EAAE,MAAM,CAAA;IACX,OAAO,CAAC,OAAO,EAAE,OAAO,CAAA;IAExB,YAAY,GAAG,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO;QACvC,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC;QACf,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACzB,CAAC;IAED,iBAAiB,CAAC,WAAW,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI;QAClD,IAAI,IAAI,CAAC,OAAO,EAAE;YAChB,IAAI,MAAM,GAAG,OAAO,CAAC;YACrB,4CAA4C;YAC5C,IAAI,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YACjC,MAAM,GAAG,MAAM,GAAG,EAAE,GAAG,IAAI,CAAC,EAAE,CAAC;YAC/B,WAAW,CAAC,GAAG,GAAG,MAAM,CAAC;SAC1B;aAAM;YACL,WAAW,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;SAC5B;IACH,CAAC;IAED,eAAe,CAAC,eAAe,EAAE,eAAe,GAAG,IAAI;QACrD,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IACrC,CAAC;IAED,wBAAwB,CAAC,OAAO,EAAE,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;QACzD,OAAO,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;gBAChB,4CAA4C;gBAC5C,IAAI,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACjC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;aAC3B;iBAAM;gBACL,IAAI,QAAQ,GAAG,OAAO,CAAC,OAAO,GAAG,aAAa,CAAA;gBAC9C,IAAI,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;oBAC3B,EAAE,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAA;iBACvB;gBACD,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE;oBAC5B,GAAG,EAAE,IAAI,CAAC,GAAG;oBACb,QAAQ,EAAE,QAAQ;iBACnB,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,EAAE;oBAC7C,YAAY,CAAC,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE;wBAC/B,IAAI,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;wBACjC,OAAO,CAAC,OAAO,GAAG,IAAI,CAAC,EAAE,CAAC,CAAA;oBAC5B,CAAC,CAAC,CAAA;oBACF,YAAY,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE;wBACtC,OAAO,CAAC,QAAQ,CAAC,CAAA;oBACnB,CAAC,CAAC,CAAA;gBACJ,CAAC,CAAC,CAAA;aACH;QACH,CAAC,CAAC,CAAA;IACJ,CAAC;CACF", "entry-package-info": "audioplayers_ohos|1.0.0"}}