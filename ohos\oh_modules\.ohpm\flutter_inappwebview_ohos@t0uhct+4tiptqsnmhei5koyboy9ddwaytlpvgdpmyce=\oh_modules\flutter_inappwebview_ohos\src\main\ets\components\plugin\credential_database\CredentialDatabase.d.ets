import CredentialDatabaseHelper from './CredentialDatabaseHelper';
import URLCredentialDao from './URLCredentialDao';
import URLProtectionSpaceDao from './URLProtectionSpaceDao';
import common from '@ohos.app.ability.common';
import URLCredential from '../types/URLCredential';
import List from '@ohos.util.List';
export default class CredentialDatabase {
    private static instance;
    static DATABASE_VERSION: number;
    static DATABASE_NAME: string;
    protectionSpaceDao: URLProtectionSpaceDao;
    credentialDao: URLCredentialDao;
    db: CredentialDatabaseHelper;
    constructor(db: CredentialDatabaseHelper, protectionSpaceDao: URLProtectionSpaceDao, credentialDao: URLCredentialDao);
    static getInstance(context: common.Context): CredentialDatabase;
    getHttpAuthCredentials(host: string, protocol: string, realm: string, port: number): Promise<List<URLCredential>>;
    setHttpAuthCredential(host: string, protocol: string, realm: string, port: number, username: string, password: string): Promise<void>;
    removeHttpAuthCredentials(host: string, protocol: string, realm: string, port: number): Promise<void>;
    removeHttpAuthCredential(host: string, protocol: string, realm: string, port: number, username: string, password: string): Promise<void>;
    clearAllAuthCredentials(): void;
}
