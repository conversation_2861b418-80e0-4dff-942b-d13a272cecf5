import List from "@ohos.util.List";
import { Any } from '@ohos/flutter_ohos';
export default class PermissionResponse {
    private resources;
    private action;
    constructor(resources: List<string>, action: number);
    static fromMap(map: Map<string, Any>): PermissionResponse | null;
    getResources(): List<string>;
    setResources(resources: List<string>): void;
    getAction(): number;
    setAction(action: number): void;
    toString(): string;
}
