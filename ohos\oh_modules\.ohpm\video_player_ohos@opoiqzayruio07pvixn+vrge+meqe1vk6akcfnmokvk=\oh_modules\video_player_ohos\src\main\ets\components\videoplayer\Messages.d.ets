import media from '@ohos.multimedia.media';
export declare function wrapError(exception: Error): Array<Object>;
export declare class CreateMessage {
    constructor();
    private asset;
    getAsset(): string;
    setAsset(setterArg: string): void;
    private uri;
    getUri(): string;
    setUri(setterArg: string): void;
    private packageName;
    getPackageName(): String;
    setPackageName(setterArg: String): void;
    private formatHint;
    getFormatHint(): String;
    setFormatHint(setterArg: String): void;
    private httpHeaders;
    getHttpHeaders(): Map<String, String>;
    setHttpHeaders(setterArg: Map<String, String>): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): CreateMessage;
}
export declare class LoopingMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(setterArg: Number): void;
    private isLooping;
    getIsLooping(): boolean;
    setIsLooping(setterArg: boolean): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): LoopingMessage;
}
export declare class MixWithOthersMessage {
    constructor();
    private mixWithOthers;
    getMixWithOthers(): Boolean;
    setMixWithOthers(setterArg: Boolean): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): MixWithOthersMessage;
}
export declare class PlaybackSpeedMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(setterArg: Number): void;
    private speed;
    getSpeed(): media.PlaybackSpeed | null;
    setSpeed(setterArg: Number): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): PlaybackSpeedMessage;
}
export declare class PositionMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(setterArg: Number): void;
    private position;
    getPosition(): Number;
    setPosition(setterArg: Number): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): PositionMessage;
}
export declare class TextureMessage {
    private textureId;
    getTextureId(): Number;
    setTextureId(setterArg: Number): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): TextureMessage;
}
export declare class VolumeMessage {
    constructor();
    private textureId;
    getTextureId(): Number;
    setTextureId(setterArg: Number): void;
    private volume;
    getVolume(): Number;
    setVolume(setterArg: Number): void;
    toList(): Array<Object>;
    static fromList(list: Array<Object>): VolumeMessage;
}
