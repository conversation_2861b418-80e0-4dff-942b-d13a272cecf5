// import 'package:flutter/cupertino.dart';
// import 'package:flutter_riverpod/flutter_riverpod.dart';
// import 'package:lib_base/log/log.dart';
// import 'package:lib_base/model/http/video_info_model.dart';
// import 'package:lib_base/utils/business/image_util.dart';
// import 'package:lib_base/utils/ui_util.dart';
// import 'package:lib_base/widgets/video/controller/m_video_player_controller.dart';
// import 'package:scrollview_observer/scrollview_observer.dart';
// import 'package:yyb_text_dubbing/util/text_dubbing_file_util.dart';

// /**
//  * 配音页面
//  */
// class TextDubbingController {
//   final WidgetRef ref;
//   final List<VideoInfoModel> videos;
//   final String resourceId;
//   final String videoUrl;
//   final String resourceAddress;
//   late MVideoPlayerController mVideoPlayerController;
//   final ListObserverController listObserverController;

//   ValueNotifier<int> indexNotifier = ValueNotifier(0);

//   bool _isPlaying = true;

//   bool get isPlaying => _isPlaying;

//   ValueNotifier<bool> showTranslateNotifier = ValueNotifier(false);

//   TextDubbingController(
//       {required this.ref,
//       required this.resourceId,
//       required this.videoUrl,
//       required this.videos,
//       required this.listObserverController,
//       required this.resourceAddress}) {
//     // String videoUrl = notifier.isSingle?  notifier.singleDubbingDeailModel?.dubbingResoure?.originalAudio ?? "": notifier.cooperateDubbingDetailModel?.dubbingResoure?.originalAudio ?? "";
//     mVideoPlayerController = MVideoPlayerController();
//     mVideoPlayerController.initController(
//         videoUrl: ImageUtil.getImageUrl(videoUrl));
//     // videos=notifier.getVideoInfo();
//   }

//   void listener() {
//     if (_isPlaying &&
//         (mVideoPlayerController.videoController?.value.isInitialized ??
//             false)) {
//       Duration? d = mVideoPlayerController.videoController!.value.position;
//       VideoInfoModel video = videos[indexNotifier.value];
//       String? endPoint = video.endPoint;
//       if (endPoint != null) {
//         double end = double.tryParse(endPoint) ?? 0;
//         int milseconds = (end * 1000).toInt();
//         Logger.info(" milseconds:$milseconds,  d:$d");
//         if (d.inMilliseconds > milseconds) {
//           mVideoPlayerController.videoController?.pause();
//           _isPlaying = false;
//         }
//       }
//     }
//   }

//   int? _getMillseconds(String? pointStr) {
//     if (pointStr != null) {
//       double? seconds = double.tryParse(pointStr);
//       if (seconds != null) {
//         int millseconds = (seconds * 1000).toInt();
//         return millseconds;
//       }
//     }
//   }

//   void doPlay() async {
//     VideoInfoModel video = videos[indexNotifier.value];
//     int? millseconds = _getMillseconds(video.startPoint);
//     Logger.info("=========== do play: $millseconds");
//     if (millseconds != null) {
//       await mVideoPlayerController.videoController
//           ?.seekTo(Duration(milliseconds: millseconds));
//     }
//     mVideoPlayerController.videoController?.play();
//     _isPlaying = true;
//   }

//   // void onItemTap(int index) {
//   //   if (indexNotifier.value != index) {
//   //     indexNotifier.value = index;
//   //     doPlay();
//   //   }
//   // }

//   // void scrollToItem(VideoInfoModel item) {
//   //   var index = videos.indexOf(item);
//   //   listObserverController.animateTo(
//   //     offset: (double offset) {
//   //       return 200;
//   //     },
//   //     index: index,
//   //     duration: const Duration(milliseconds: 250),
//   //     curve: Curves.ease,
//   //   );
//   //   indexNotifier.value = index;
//   // }

//   // void onTranslateTap() {
//   //   showTranslateNotifier.value = !showTranslateNotifier.value;
//   // }

//   // /**
//   //  * 下载并解压资源包
//   //  */
//   // void downloadDubbingResource() async {
//   //   showLoading();
//   //   // String resourceAddress=notifier.isSingle?  notifier.singleDubbingDeailModel?.dubbingResoure?.resourceAddress ?? "": notifier.cooperateDubbingDetailModel?.dubbingResoure?.resourceAddress ?? "";
//   //   // String resourceId=notifier.resourceId;
//   //   TextDubbingFileUtil.downloadAndExtractZip(
//   //           resourceId, ImageUtil.getImageUrl(resourceAddress))
//   //       .whenComplete(() => dismissLoading());
//   // }
// }
