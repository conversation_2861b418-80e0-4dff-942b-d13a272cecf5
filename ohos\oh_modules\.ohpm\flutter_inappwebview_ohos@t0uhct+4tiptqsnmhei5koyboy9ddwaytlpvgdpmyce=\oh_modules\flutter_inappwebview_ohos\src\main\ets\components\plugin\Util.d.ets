import { Any } from '@ohos/flutter_ohos';
export default class Util {
    static LOG_TAG: string;
    constructor();
    static replaceAll(s: string, oldString: string, newString: string): string;
    static getOrDefault(map: Map<string, Any>, key: string, defaultValue: Any): Any;
    static objEquals(a: Any, b: Any): boolean;
    static formatString(format: string, ...args: Any[]): string;
    static getPixelDensity(): number;
}
