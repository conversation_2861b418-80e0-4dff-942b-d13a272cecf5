{"name": "singsound", "version": "1.0.0", "description": "Please describe the basic information.", "main": "Index.js", "author": "", "license": "Apache-2.0", "dependencies": {"libsingsound.so": "file:./src/main/cpp/types/libsingsound"}, "types": "Index.d.ets", "artifactType": "obfuscation", "metadata": {"runtimeOnly": {"sources": ["./src/main/ets/pages/Test.js"], "packages": []}, "sourceRoots": ["./src/main"], "debug": false, "nativeDebugSymbol": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true, "nativeComponents": [{"name": "libsingsound.so", "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "linkLibraries": []}]}