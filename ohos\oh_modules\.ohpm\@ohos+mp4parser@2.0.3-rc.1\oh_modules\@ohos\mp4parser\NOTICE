OPEN SOURCE SOFTWARE NOTICE

Please note we provide an open source software notice for the third party open source software along with this software and/or this software component (in the following just “this SOFTWARE”). The open source software licenses are granted by the respective right holders.

Warranty Disclaimer
THE OPEN SOURCE SOFTWARE IN THIS PRODUCT IS DISTRIBUTED IN THE HOPE THAT IT WILL BE USEFUL, BUT WITHOUT ANY WARRANTY, WITHOUT EVEN THE IMPLIED WARRANTY OF MERCHANTABILITY OR FITNESS FOR A PARTICULAR PURPOSE. SEE THE APPLICABLE LICENSES FOR MORE DETAILS.

Copyright Notice and License Texts

----------------------------------------------------------------------

Software: mp4parser

Copyright notice:

Copyright 2012 <PERSON>, Hamburg

Copyright 2008 CoreMedia AG, Hamburg



License: Apache License V2.0
Unless specifically indicated otherwise in a file, files are licensed
under the Apache 2.0 license, as can be found in: LICENSE



Software: FFmpeg

Copyright notice:

Copyright (c) 2003 <PERSON>

Copyright (C) 2001-2011 <PERSON> <micha<PERSON><PERSON>@gmx.at>

Copyright (c) 2000, 2001, 2003 Fabrice Bellard

Copyright (c) 2007 Bobby Bingham

Copyright (c) 2011 Stefano Sabatini

Copyright (c) 2008 Vitor Sessak


License:
Most files in FFmpeg are under the GNU Lesser General Public License version 2.1 or later (LGPL v2.1+).
Read the file COPYING.LGPLv2.1 for details. Some other files have MIT/X11/BSD-style licenses. In combination the LGPL v2.1+ applies to FFmpeg.

Most files in FFmpeg are under the GNU Lesser General Public License version 2.1 or later (LGPL v2.1+). Read the file COPYING.LGPLv2.1 for details. Some other files have MIT/X11/BSD-style licenses. In combination the LGPL v2.1+ applies to FFmpeg.

Some optional parts of FFmpeg are licensed under the GNU General Public License version 2 or later (GPL v2+). See the file COPYING.GPLv2 for details. None of these parts are used by default, you have to explicitly pass --enable-gpl to configure to activate them. In this case, FFmpeg's license changes to GPL v2+.



Software: FFmpeg-Invoker

Copyright notice: 

Copyright (c) 2020-present. xch168


License:

Copyright (c) 2020-present. xch168

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.

Software: FFmpegMediaMetadataRetriever

Copyright notice: 

Copyright 2020 William Seemann


License:

FFmpegMediaMetadataRetriever: A unified interface for retrieving frame 
and meta data from an input media file.

Copyright 2020 William Seemann

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.


