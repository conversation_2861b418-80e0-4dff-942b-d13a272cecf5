# 1.0.11
- Fix issue because of conflicted `AppLink` scheme

# 1.0.10
- Support launching WeChat with `openLink`

# 1.0.9
- Support `WXFileObject`

# 1.0.8
- Fix issue for serialization

# 1.0.7
- Support `LaunchFromWXReq`

# 1.0.6
- Support `WXWebpageObject` & `WXMiniProgramObject` for `WXMediaMessage.mediaObject`

# 1.0.5
- Support `OpenBusinessView`

# 1.0.4
- Support `isWXAppInstalled`
- Add consumer-rules for obfuscation

# 1.0.3
- Support `LaunchMiniProgram`

# 1.0.2
- Fix issue `WXApi.sendReq` never returns (promise does not resolve) when `startAbility` succeed

# 1.0.1
- Add `callbackAbility` in `BaseReq`

# 1.0.0

init WeChat OpenSDK
