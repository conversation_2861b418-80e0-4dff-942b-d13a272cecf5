{"app": {"signingConfigs": [{"name": "default", "type": "HarmonyOS", "material": {"certpath": "C:\\Users\\<USER>\\.ohos\\config\\default_ohos_efqy5cCLQ5GOYdEYaiRHctV-XouwM1QY4MBAuJlpJMs=.cer", "keyAlias": "debugKey", "keyPassword": "0000001ADA550264631997BAD3380CFE50A1A62DBCF794C709FBF200609B6953C74D2103CF4977A736D6", "profile": "C:\\Users\\<USER>\\.ohos\\config\\default_ohos_efqy5cCLQ5GOYdEYaiRHctV-XouwM1QY4MBAuJlpJMs=.p7b", "signAlg": "SHA256withECDSA", "storeFile": "C:\\Users\\<USER>\\.ohos\\config\\default_ohos_efqy5cCLQ5GOYdEYaiRHctV-XouwM1QY4MBAuJlpJMs=.p12", "storePassword": "0000001A47E367699944D3EDA8B86507D59B39B92DBFA925579CDB3701FA5D3D9364A9014EE4DE154C87"}}, {"name": "release", "type": "HarmonyOS", "material": {"certpath": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/eyyb.cer", "storePassword": "00000018175DF4AF554B30DF8159AD878563C1CCD413C446B80C5776464D81934C95DC9A7DF93805", "keyAlias": "key", "keyPassword": "00000018A4839ADCDF8B9E142BA80A4886911B152C32EFB6280E2F1591C8A806ABE5F1004ADA9BD2", "profile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/eyybRelease.p7b", "signAlg": "SHA256withECDSA", "storeFile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/.p12"}}, {"name": "debug", "type": "HarmonyOS", "material": {"storeFile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/debug/.p12", "storePassword": "000000180AC1BF370F503990122B15CD363F35611FFB2DB3A862D8FB218CCF3E11EFB8D2968F0E64", "keyAlias": "key", "keyPassword": "00000018F31D03609CD98AF711E4527715812D5BC631A12B28EEB78F3C721B12D8F57ECF74E9D861", "signAlg": "SHA256withECDSA", "profile": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/debug/eyyb_debug.p7b", "certpath": "D:/workspace/dev/harmonyOs/yyb_flutter/ohos_cert/debug/eyyb_debug.cer"}}], "products": [{"name": "default", "signingConfig": "debug", "compatibleSdkVersion": "5.0.0(12)", "runtimeOS": "HarmonyOS", "buildOption": {"strictMode": {"useNormalizedOHMUrl": true}}}]}, "modules": [{"name": "entry", "srcPath": "./entry", "targets": [{"name": "default", "applyToProducts": ["default"]}]}]}