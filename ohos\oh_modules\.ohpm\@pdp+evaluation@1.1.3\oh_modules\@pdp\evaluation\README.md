**Evaluation**
***
***介绍***

Evaluation是基于先声评测封装的仅供书本库@pdp/book使用的评测引擎库
***

***如何安装***
```shell
ohpm install @pdp/evaluation
```
***
***如何使用***

****备注：在使用之前请先申请麦克风权限，使用以下方法申请****
```typescript
const grant = await EvaluationManager.requestPermission()
if (grant) {
  // 获取到权限
}
```

1、初始化 
```typescript
evaluationManager = new EvaluationManager()
```
2、设置评测回调
```typescript
evaluationManager.callBack = (async (result?: EvaluationResult|null)=>{
  // 在此处理评测结果
})
```
3、开始评测
```typescript
// 指定评测文字及评测类型
evaluationManager.start('这是评测文字', CoreType.cn_phrases)
```
4、结束评测
```typescript
// 在调用该方法后，会进行第二步的评测回调
evaluationManager.stop()
```
