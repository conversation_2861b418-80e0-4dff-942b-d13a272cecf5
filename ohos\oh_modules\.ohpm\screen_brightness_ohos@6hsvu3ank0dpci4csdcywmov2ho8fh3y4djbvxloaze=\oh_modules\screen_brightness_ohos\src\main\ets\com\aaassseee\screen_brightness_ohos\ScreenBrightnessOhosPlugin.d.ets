import { FlutterPlugin, FlutterPluginBinding, Method<PERSON>all, <PERSON>R<PERSON><PERSON>, MethodCallHandler } from "@ohos/flutter_ohos";
export default class ScreenBrightnessOhosPlugin implements FlutterPlugin, MethodCallHandler {
    private methodChannel?;
    private systemScreenBrightness?;
    private applicationScreenBrightness?;
    private abilityPluginBinding;
    private mainWindow;
    private systemScreenBrightnessChangedEventChannel?;
    private systemScreenBrightnessChangedStreamHandler;
    private applicationScreenBrightnessChangedEventChannel?;
    private applicationScreenBrightnessChangedStreamHandler;
    private isAutoReset;
    private isAnimate;
    onMethodCall(call: MethodCall, result: MethodResult): void;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    private getWindow;
    private getSystemScreenBrightness;
    private handleGetSystemScreenBrightnessMethodCall;
    private handleSetSystemScreenBrightnessMethodCall;
    private handleGetApplicationScreenBrightnessMethodCall;
    private handleSetApplicationScreenBrightnessMethodCall;
    private handleResetApplicationScreenBrightnessMethodCall;
    private handleApplicationScreenBrightnessChanged;
    private handleHasApplicationScreenBrightnessChangedMethodCall;
    private handleIsAutoResetMethodCall;
    private handleSetAutoResetMethodCall;
    private handleIsAnimateMethodCall;
    private handleSetAnimateMethodCall;
    private handleCanChangeSystemBrightness;
}
