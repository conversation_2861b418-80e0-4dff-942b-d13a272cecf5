import { MethodCall } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import CredentialDatabase from './CredentialDatabase';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
export default class CredentialDatabaseHandler extends ChannelDelegateImpl {
    credentialDatabase: CredentialDatabase | null;
    plugin: InAppWebViewFlutterPlugin | null;
    constructor(plugin: InAppWebViewFlutterPlugin);
    init(plugin: InAppWebViewFlutterPlugin): void;
    onMethodCall(call: MethodCall, result: MethodResult): void;
    private getAllAuthCredentials;
    private getHttpAuthCredentials;
    dispose(): void;
}
