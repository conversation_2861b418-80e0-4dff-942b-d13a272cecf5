import { FlutterSoundRecorderCallback } from '../plugin/FlutterSoundRecorderCallback';
import { t_AUDIO_SOURCE, t_CODEC, t_RECORDER_STATE } from '../plugin/FlutterSoundTypes';
export declare class FlutterRecorder {
    private recorder;
    m_callback: FlutterSoundRecorderCallback | null;
    private m_path;
    private subsDurationMillis;
    private intervalID;
    private mPauseTime;
    private mStartPauseTime;
    private mStartTime;
    private status;
    constructor(callback: FlutterSoundRecorderCallback);
    openRecorder(): Promise<boolean>;
    closeRecorder(): Promise<void>;
    isEncoderSupported(codec: t_CODEC): boolean;
    getRecorderState(): t_RECORDER_STATE;
    deleteRecord(radical: string): Promise<boolean>;
    private clearTimer;
    private setTimer;
    startRecorder(codec: t_CODEC, sampleRate: number, numChannels: number, bitRate: number, path: string, audioSource: t_AUDIO_SOURCE, toStream: boolean): Promise<boolean>;
    recordingData(data: ArrayBuffer): void;
    stopRecorder(): Promise<void>;
    pauseRecorder(): Promise<void>;
    resumeRecorder(): Promise<void>;
    setSubsDurationMillis(durationMillis: number): void;
    temporayFile(radical: string): string;
    private getPath;
    private stop;
}
