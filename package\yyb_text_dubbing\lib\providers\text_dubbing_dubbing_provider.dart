import 'dart:convert';
import 'dart:core';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/log/log_util.dart';
import 'package:lib_base/model/sing_sound_result.dart';
import 'package:lib_base/model/sing_sound_result_info.dart';
import 'package:lib_base/providers/books/english/english_book_info_provider.dart';
import 'package:lib_base/providers/user/user_info_provider.dart';
import 'package:lib_base/api/api_repository.dart';
import 'package:lib_base/utils/business/dubbing_util.dart';
import 'package:lib_base/utils/business/free_count_util.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/business/sing_sound_dubbing_util.dart';
import 'package:lib_base/utils/ffmpeg_util.dart';
import 'package:lib_base/widgets/animation/image_switch_animation.dart';
import 'package:lib_base/widgets/dialog/simple_alert_dialog.dart';
import 'package:yyb_text_dubbing/model/dubbing_result_item.dart';
import 'package:yyb_text_dubbing/src/api/api_repository.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:lib_base/model/text_dubbing_detail_model.dart';
import 'package:yyb_text_dubbing/model/http/dubbing_rank_result.dart';
import 'package:yyb_text_dubbing/model/http/dubbing_video_info_model.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/model/http/text_dubbing_word_model.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/widgets/dubbing_upload_failed_dialog.dart';
import 'package:lib_base/enums/oss_upload_item.dart';
import 'package:lib_base/model/http/video_info_model.dart';
import 'package:lib_base/utils/ali_oss_util.dart';
import 'package:lib_base/widgets/dialog/simple_upload_progress_dialog.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/widgets/text_dubbing_result_item.dart';
import 'package:yyb_text_dubbing/util/singsound_record_and_play_controller.dart';
import 'package:yyb_text_dubbing/util/text_dubbing_file_util.dart';

part 'text_dubbing_dubbing_provider.g.dart';

@riverpod
class TextDubbingDubbingProvider extends _$TextDubbingDubbingProvider
    with ControlNotifierModelMixin {
  String resourceId = "";
  String bookId = "";
  String? source; // 添加source字段
  TextDubbingDetailModel? dubbingDetailModel;

  //当前正在配音的句子
  ValueNotifier<DubbingVideoInfoModel> currentDubbing =
      ValueNotifier(DubbingVideoInfoModel());
  //是否完成了配音上传（查看配音报告）
  ValueNotifier<bool> ifShowReport = ValueNotifier(false);

  //当前选中tab
  ValueNotifier<int> tabIndexNotifier = ValueNotifier(0);

  //免费测评当前已使用的次数
  ValueNotifier<int> freeCount = ValueNotifier(0);

  //当前激活的语句
  ValueNotifier<int> activeIndexNotifier = ValueNotifier(-1);

  //翻译状态  0 翻译  1  不翻译
  ValueNotifier<int> translateTypeNotifier = ValueNotifier(0);

  //评测语句列表
  ValueNotifier<List<DubbingVideoInfoModel>> videoInfoList = ValueNotifier([]);

  //记录录音时间
  int? startDubbingTimestamp;
  int? latestDubbingTimestamp;
  //录音结果集合
  Map<String, SingsoundResult> dubbingResultMap = {};

  //当前是否正在播放原音 （发音详情页面）
  ValueNotifier<bool> isPlayingOrigin = ValueNotifier(false);
  //当前是否正在播放录音 （发音详情页面）
  ValueNotifier<bool> isPlayingRecord = ValueNotifier(false);

  //已完成章节
  ValueNotifier<List<String>> completeListNotifier = ValueNotifier([]);
  //评测结果变更序号
  ValueNotifier<int> singsoundResultUpdateIndexNotifier = ValueNotifier(-1);

  late SingSoundDubbingController singSoundDubbingController;

  bool mIsAddedPoint = false;

  // DubbingVideoInfoModel? get videoInfo => state;

  //用于控制发音详情页录音播放的控制器
  late SingsoundRecordAndPlayController recordController;
  // 发音详情页回放动画控制
  List<GlobalKey<ImageSwitchAnimationState>> playbackAnimateControllers = [];
  GlobalKey<ImageSwitchAnimationState> currentAnimateController =
      GlobalKey<ImageSwitchAnimationState>();

  //单词练习页面
  ValueNotifier<TextDubbingWordModel> word =
      ValueNotifier(TextDubbingWordModel());
  bool isWordInfoFinish = false; //查询单词是否完成
  ValueNotifier<SingsoundResult> wordResult =
      ValueNotifier(SingsoundResult()); //单词录音的评分结果

  //专业测评页面
  ValueNotifier<List<Details>> sentenceDetails = ValueNotifier([]);
  ValueNotifier<SingsoundResult> evaluateResult =
      ValueNotifier(SingsoundResult()); //专业测评录音的评分结果
  GlobalKey<ImageSwitchAnimationState> evaluateRepalyKey =
      GlobalKey<ImageSwitchAnimationState>();

  @override
  List<TextDubbingDetailModel> build() {
    // deleteFolder();//删除录音文件，测试用
    //设置免费测评的次数
    freeCount.value = FreeCountUtil.getNewSpeechProfessionalFreeCount();

    singSoundDubbingController = SingSoundDubbingController();
    recordController = SingsoundRecordAndPlayController();
    ref.onDispose(() {
      singSoundDubbingController.dispose();
      recordController.dispose();
    });
    return [];
  }

  Future<void> deleteFolder() async {
    Logger.info("课文配音文件已删除");
    String folderPath = await TextDubbingFileUtil.businessFolderPath();
    final directory = Directory(folderPath);
    if (directory.existsSync()) {
      directory.deleteSync(recursive: true);
    } else {}
  }

  createPlaybackAnimateControllers() {
    for (int i = 0; i < videoInfoList.value.length; i++) {
      playbackAnimateControllers.add(GlobalKey<ImageSwitchAnimationState>());
    }
    currentAnimateController = playbackAnimateControllers.first;
  }

  List<TextDubbingResultItem> getDubbingResultItemList() {
    Map<String, SingsoundResult> singsoundResult = dubbingResultMap;
    int dubbingDurationTime =
        (latestDubbingTimestamp ?? 0) - (startDubbingTimestamp ?? 0);
    //这里将结果记录下来
    //转换
    Map<String, SingsondResultModel> result_model = {};
    List<TextDubbingResultItem> list = [];
    for (int i = 0; i < videoInfoList.value.length; i++) {
      DubbingVideoInfoModel info_m = videoInfoList.value[i];
      VideoInfoModel mm = _modelTrans(info_m);
      SingsoundResult? result_m = singsoundResult[info_m.id ?? ""];
      double star_c = DubbingUtil.getActiveStar(result_m?.overall ?? 0);
      SingsondResultModel resm = SingsondResultModel(
          model: mm,
          singsoundResult: result_m,
          content: mm.english,
          starCount: star_c);
      result_model[info_m.id ?? ""] = resm;

      list.add(TextDubbingResultItem(
          result_model, info_m.id ?? "", dubbingDurationTime));
    }
    return list;
  }

  DubbingResultItem getDubbingTotalResultItem() {
    Map<String, SingsoundResult> singsoundResult = dubbingResultMap;
    int dubbingDurationTime =
        (latestDubbingTimestamp ?? 0) - (startDubbingTimestamp ?? 0);
    //这里将结果记录下来
    //转换
    Map<String, SingsondResultModel> result_model = {};

    for (int i = 0; i < videoInfoList.value.length; i++) {
      DubbingVideoInfoModel info_m = videoInfoList.value[i];
      VideoInfoModel mm = _modelTrans(info_m);
      SingsoundResult? result_m = singsoundResult[info_m.id ?? ""];
      double star_c = DubbingUtil.getActiveStar(result_m?.overall ?? 0);
      SingsondResultModel resm = SingsondResultModel(
          model: mm,
          singsoundResult: result_m,
          content: mm.english,
          starCount: star_c);
      result_model[info_m.id ?? ""] = resm;
    }
    return DubbingResultItem(result_model, dubbingDurationTime);
  }

  TextDubbingResultItem getDubbingResultItem(DubbingVideoInfoModel model) {
    int dubbingDurationTime =
        (latestDubbingTimestamp ?? 0) - (startDubbingTimestamp ?? 0);
    //这里将结果记录下来
    //转换
    Map<String, SingsondResultModel> result_model = {};

    String id = model.id ?? "";
    VideoInfoModel mm = _modelTrans(model);
    SingsoundResult? result_m = dubbingResultMap[id];
    double star_c = DubbingUtil.getActiveStar(result_m?.overall ?? 0);
    SingsondResultModel resm = SingsondResultModel(
        model: mm,
        singsoundResult: result_m,
        content: mm.english,
        starCount: star_c);
    result_model[id] = resm;

    return TextDubbingResultItem(result_model, id, dubbingDurationTime);
  }

  TextDubbingResultItem getEvaluateDubbingResultItem(
      DubbingVideoInfoModel model) {
    String key = model.id ?? "";
    Map<String, SingsoundResult> singsoundResult = {key: evaluateResult.value};
    int dubbingDurationTime =
        (latestDubbingTimestamp ?? 0) - (startDubbingTimestamp ?? 0);
    //这里将结果记录下来
    //转换
    Map<String, SingsondResultModel> result_model = {};

    String id = model.id ?? "";
    VideoInfoModel mm = _modelTrans(model);
    SingsoundResult? result_m = singsoundResult[id];
    double star_c = DubbingUtil.getActiveStar(result_m?.overall ?? 0);
    SingsondResultModel resm = SingsondResultModel(
        model: mm,
        singsoundResult: result_m,
        content: mm.english,
        starCount: star_c);
    result_model[id] = resm;

    return TextDubbingResultItem(result_model, id, dubbingDurationTime);
  }

  ControlNotifierModel? get currentActiveModel {
    int activeIndex =
        activeIndexNotifier.value <= 0 ? 0 : activeIndexNotifier.value;
    int length = videoInfoList.value.length;
    if (length > activeIndex) {
      String id = videoInfoList.value[activeIndex].id ?? "";
      return getControlModel(id);
    }
  }

// DubbingVideoInfoModel? build(){
//
//   return null;
// }

  reinitPage() {
    dubbingResultMap.clear();
    singsoundResultUpdateIndexNotifier.value = -1;
    activeIndexNotifier.value = -1;
  }

  void pauseAll() {
    var model = currentActiveModel;
    if (model == null) {
      singSoundDubbingController.stopRecordAndOrigin();
    } else {
      singSoundDubbingController.doPauseAll(model);
    }
  }

  void switchTranslateState() {
    if (translateTypeNotifier.value == 1) {
      translateTypeNotifier.value = 0;
    } else {
      translateTypeNotifier.value = 1;
    }
  }

  void goback() {
    if (dubbingResultMap.isNotEmpty) {
      showSmartDialog(SimpleAlertDialog(
        title: "等等，先别走",
        desc: "现在离开，刚才的进度就没有了哦~",
        confirmText: "继续努力",
        cancelText: "确定",
        confirm: () {
          dismissDialog();
        },
        cancel: () {
          dismissDialog();
          back();
        },
      ));
    } else {
      back();
    }
  }

  Future<bool> doRecord(int seconds, DubbingVideoInfoModel item) async {
    var model = getControlModel(item.id ?? "");
    if (!singSoundDubbingController.isRecording) {
      singSoundDubbingController.doPauseAll(model);
      //开始录音
      //记录录音时间
      _noteDubbingTime();
      Logger.info(
          "doRecord.filepath===${TextDubbingFileUtil.audioRecordFile(resourceId, item.id!)}");
      SingsoundResult? singSoundResult =
          await singSoundDubbingController.startRecord(
              seconds,
              item.english ?? "",
              await TextDubbingFileUtil.audioRecordFile(resourceId, item.id!),
              model);
      Logger.info("11singSoundResult===${singSoundResult?.overall ?? 0}");
      Logger.info(
          "TextDubbingResultItem===${singSoundResult?.integrity},,${singSoundResult?.accuracy},,${singSoundResult?.fluency?.overall}");
      if (singSoundResult != null) {
        if ((singSoundResult.overall ?? 0) < 20) {
          showToast("此句录音得分较低，请重新录制!");
          // if(!dubbingResultMap.containsKey(item.id!)){
          //   showToast("此句录音得分较低，请重新录制!");
          // }
        } else {
          dubbingResultMap[item.id!] = singSoundResult;
          Logger.info(
              "============= dubbingResultMap key:${item.id}, result:${dubbingResultMap[item.id!]}");
        }
        int index = videoInfoList.value.indexOf(item);
        if (singsoundResultUpdateIndexNotifier.value == index) {
          singsoundResultUpdateIndexNotifier.value = -1;
        }
        singsoundResultUpdateIndexNotifier.value = index;
        model.replayHasResultStateNotifier?.value = model.notifierFlag;

        return true;
      }
    } else {
      Logger.info('singSoundDubbingController.doPauseAll');
      //停止录音
      singSoundDubbingController.doPauseAll(model);
    }
    return false;
  }

  void doReplay(DubbingVideoInfoModel item) async {
    var model = getControlModel(item.id ?? "");
    Logger.info("doReplay===${item.id ?? ""},,${model}");
    if (dubbingResultMap.containsKey(item.id)) {
      if (singSoundDubbingController.isReplaying) {
        singSoundDubbingController.doPauseAll(model);
      } else {
        singSoundDubbingController.doPauseAll(model);
        String filePath =
            await TextDubbingFileUtil.audioRecordFile(resourceId, item.id!);
        singSoundDubbingController.startReplay(filePath, model);
      }
    } else {
      singSoundDubbingController.doPauseAll(model);
      showToast("请先录音");
    }
  }

  List<VideoInfoModel> _getVideos() {
    List<VideoInfoModel> res = [];
    for (int i = 0; i < videoInfoList.value.length; i++) {
      DubbingVideoInfoModel info = videoInfoList.value[i];
      VideoInfoModel model = _modelTrans(info);
      res.add(model);
    }
    Logger.info("_getVideos===${res.length}");
    return res;
  }

  VideoInfoModel _modelTrans(DubbingVideoInfoModel info) {
    VideoInfoModel model = VideoInfoModel();
    model.id = info.id;
    model.endPoint = info.endPoint;
    model.english = info.english;
    model.role = info.role;
    model.sort = info.sort;
    model.startPoint = info.startPoint;
    model.translation = info.translation;

    return model;
  }

  //记录配音时间
  void _noteDubbingTime() {
    latestDubbingTimestamp = DateTime.now().millisecondsSinceEpoch;
    if (startDubbingTimestamp == null) {
      startDubbingTimestamp = latestDubbingTimestamp;
    }
  }

  // 合并录音音频和背景音频
  Future<VideoInfoModel?> mergeRecordAudioAndBackGroundAudio(
      String videoCove) async {
    showLoading();
    var userInfo = ref.read(userInfoNotifierProvider);
    String roleId = userInfo.userId;

    List<VideoInfoModel> videos = _getVideos();
    Logger.info("合并前===${videoInfoList.value.length}");
    try {
      // if (roleId != 'all') {
      //   //过滤角色
      //   videos = videos.where((element) => element.role == roleId).toList();
      // }

      //没有配音的video
      List<VideoInfoModel> lostVideos = videos.toList();
      //检查录音音频数量是否满足
      //文件数量相等
      List<String> files = await _audioRecordFiles(videos, lostVideos);
      Logger.info(
          "files.length:${files.length}, videos.length:${videos.length}, lostVideos.length:${lostVideos.length}, resourceId:$resourceId");
      if (lostVideos.isEmpty) {
        //配音正常
        String resourcePath =
            await TextDubbingFileUtil.resourceFolderPath(resourceId);
        String backFile = "$resourcePath/${TextDubbingFileUtil.backFileName}";
        String mergedFolder =
            await TextDubbingFileUtil.audioVideoMergeFolderPath(resourceId);
        String mergedAudioFile =
            await TextDubbingFileUtil.dubbingAudioFile(resourceId);
        Logger.info(
            "============音频${mergedAudioFile} 存在:${File(mergedAudioFile).existsSync()}");
        //删除已经存在的音频
        _deleteFile(mergedAudioFile);
        Logger.info(
            "============音频${mergedAudioFile} 删除后:${File(mergedAudioFile).existsSync()}");
        //删除已经存在的音频
        //合并音频
        Logger.info("============backFile exist:${File(backFile).existsSync()}，  files:${files.length}, videos:${videos.length}, mergedAudioFile:$mergedAudioFile}");
        await FFMpegUtil.doMergeAudio(backFile, files, videos, mergedAudioFile);
        //删除 录制的音频
        // _deleteRecordAudioFiles(files);

        // 基本视频
        String baseVideo =
            "$resourcePath/${TextDubbingFileUtil.baseVideoFileName}";

        String dubbingVideo =
            "$mergedFolder/${TextDubbingFileUtil.dubbingVideoFileName}";
        //删除已经存在的视频
        _deleteFile(dubbingVideo);
        await FFMpegUtil.mergeAudioAndVideo(
            mergedAudioFile, baseVideo, dubbingVideo);

        Logger.info("output video:$dubbingVideo");

        // 评分

        // param.setSingsoundResult(singsoundResult, dubbingDuraionTime);
        // toPage(RouteName.dubbingPreview,
        //     queryParameters: {
        //       "videoPath": dubbingVideo,
        //     },
        //     extra: param);
      } else {
        Logger.info(
            "==== files.length:${files.length}==videos.length:${videos.length}");
        VideoInfoModel lost = lostVideos[0];
        //  配音没配完
        showToast("请先完成配音");

        return lost;
      }
    } finally {
      dismissLoading();
    }
  }

  Future<List<String>> _audioRecordFiles(
      List<VideoInfoModel> videos, List<VideoInfoModel> lostVideos) async {
    List<String> files = [];
    String audioResourceFolder =
        await TextDubbingFileUtil.audioRecordFolderPath(resourceId);
    Logger.info("获取的录音文件存放的路径===${audioResourceFolder}");
    // /data/storage/el2/base/cache/eyyb/text_dubbing/S6DDuWPcHjVMj3ZLnybXqs/audioRecorder
    for (VideoInfoModel videoInfoModel in videos) {
      File temp = File("$audioResourceFolder/${videoInfoModel.id ?? ""}.wav");
      Logger.info("获取的录音文件===${temp.path},,,${temp.existsSync()}");
      if (temp.existsSync()) {
        files.add(temp.path);
        lostVideos.remove(videoInfoModel);
      }
    }
    return files;
  }

  //删除已经录制的音频
  void _deleteRecordAudioFiles(List<String> files) {
    for (String f in files) {
      var file = File(f);
      if (file.existsSync()) {
        file.delete();
      }
    }
  }

  void _deleteFile(String file) {
    File outputFile = File(file);
    if (outputFile.existsSync()) {
      outputFile.deleteSync();
    }
  }

  void submitDubbing(String filePath) async {
    Logger.info(
        "submitDubbing： filePath:$filePath， file exist:${File(filePath).existsSync()}");
    var userInfo = ref.read(userInfoNotifierProvider);
    String userId = userInfo.userId;
    if (dubbingResultMap.isNotEmpty) {
      // ApiRepository.saveuseryypccompletionprog(userId: userInfo.userId,paramId: state?.unitId??"",subId:  state?.parentId??"",bookId: param.bookId,bookUnitId: param.unitItem.id??"");
      //  更新积分  PointReceiver
      // BroadcastUtils.addPoint(NewSpeechListActivity.this, PointRuleEnum.eFinishSpeechEvaluationRead,
      //     "20", mUnitItem.getOralunitId());
      // BaseApiRepository.updatePoint(ruleCode: PointRuleType.eFinishSpeechEvaluationRead, moduleId: "20", detailId: state?.parentId??"");

      //计算得分
      //每一句总得分
      num machineScore = 0;
      String totalScore = "";
      for (int i = 0; i < videoInfoList.value.length; i++) {
        DubbingVideoInfoModel model = videoInfoList.value[i];
        SingsoundResult? result = dubbingResultMap[model.id];
        Logger.info("submitDubbing===${result?.overall ?? 0}");
        if (result != null) {
          machineScore += result.overall ?? 0;
          if (i < videoInfoList.value.length - 1) {
            totalScore += "${(result.overall ?? 0)},";
          } else {
            totalScore += "${(result.overall ?? 0)}";
          }
        }
      }
      showLoading();
      var response = await ApiRepository.querySubmitDubbingRanking(
              userId: userId,
              resourceId: resourceId,
              machineScore: machineScore.toString())
          .whenComplete(() {
        dismissLoading();
      });
      // dismissLoading();
      Logger.info("querySubmitDubbingRanking");
      if (response.isSuccess && response.isDataNotNull) {
        DubbingRankResult model = response.dataNotNull;
        int ranking = (model.ranking ?? 0).toInt();
        //0代表进入了300名，1代表300名之外
        if (ranking == 0) {
          //上传配音视频
          SimpleUploadProgressDialog.showDialog(
              files: [filePath],
              ossUploadItem: OssUploadItem.UPLOAD_RECORD,
              onComplete: (List<OssUploadResponse> result) async {
                dismissDialog();
                dismissLoading();
                showLoading(clickMaskDismiss: true, backDismiss: true);
                if (result.length == 0) {
                  //弹窗提示重新上传
                  showSmartDialog(
                    DubbingUploadFailedDialog(
                      confirmCallback: () {
                        submitDubbing(filePath);
                      },
                    ),
                  );
                } else {
                  OssUploadResponse res = result.first;
                  String url = res.downloadUrl ?? "";
                  Logger.info("1上传成功：downloadUrl=${url}");
                  // for (Map.Entry<String, String> entry : entries) {
                  //   String key = entry.getKey();
                  //   String value = entry.getValue();
                  //   LogUtils.e(key + value);
                  //   videoUploadJsonItem.setAudioFilePath(value);
                  //   videoUploadJsonItem.setResourceId(mDivisionSourceBean.getResourceId());
                  //   videoUploadJsonItem.setResourceAddress(mDivisionSourceBean.getResourceAddress());
                  //   videoUploadJsonItem.setImageUrl(mDivisionSourceBean.getResourceCover());
                  //   mJson = JSON.toJSONString(videoUploadJsonItem);
                  // }

                  //提交
                  _submitWithFileUrl(totalScore, url);
                }
              });
        } else {
          //直接提交
          _submitWithFileUrl(totalScore, OssUploadItem.UPLOAD_RECORD);
        }
      } else {
        showToast("提交失败，请稍后重试");
      }
    }
  }

  Future<void> saveKwpyCompletionProgress(
      String userId, String totalScore) async {
    //更新课文配音完成进度
    // 参数: bookId, paramId, subId, userId, studyTime, remark

    String? unitId =
        ref.read(englishBookInfoProviderProvider.notifier).currentUnitId;
    var response = await BaseApiRepository.saveuserkwpycompletionprog(
      bookId: bookId,
      paramId: unitId ?? "", // 默认兜底id，防止接口报错
      subId: bookId,
      userId: userId,
      // 计算学习时长，保证为正整数且不超过一天
      studyTime: (() {
        int studyTime =
            ((latestDubbingTimestamp ?? 0) - (startDubbingTimestamp ?? 0));
        if (studyTime <= 0) studyTime = 1;
        if (studyTime > 86400) studyTime = 86400;
        return studyTime;
      })(),
      remark: totalScore, // 使用配音得分作为备注
    );

    LogUtil.doLog(
        "1111111 saveuserkwpycompletionprog response=========  ${response.isSuccess}");
  }

  Future<void> _submitWithFileUrl(
      String totalScore, String audioFilePath) async {
    var userInfo = ref.read(userInfoNotifierProvider);
    String userId = userInfo.userId;
    String schoolId = userInfo.obj?.schoolId ?? "";

    Map<String, dynamic> data = {
      'resourceId': resourceId,
      'resourceAddress':
          dubbingDetailModel?.dubbingResoure?.resourceAddress ?? "",
      'imageUrl': dubbingDetailModel?.dubbingResoure?.videoCove ?? "",
      'audioFilePath': audioFilePath,
    };

    var jsonStr = jsonEncode(data);
    showLoading();
    Logger.info("jsonStr===$jsonStr");
    var resp = await ApiRepository.submitdubbing(
            userId: userId,
            resourceId: resourceId,
            dubbingInfo: jsonStr,
            totalScore: totalScore,
            schoolId: schoolId,
            videoName: dubbingDetailModel?.dubbingResoure?.videoName ?? "",
            dubbingType: "1",
            bookName: dubbingDetailModel?.dubbingResoure?.bookName,
            unitName: dubbingDetailModel?.dubbingResoure?.unitName)
        .whenComplete(() {
      dismissLoading();
    });

    if (resp.isSuccess && resp.isDataNotNull) {
      //??
      Logger.info("jsonStr===${resp.dataNotNull}");
      //是个id ，不知道干嘛用的
      showToast("提交配音成功");
      //显示报告
      ifShowReport.value = true;
    }

    // 只有当source是'englishSyn'时才调用saveuserkwpycompletionprog
    if (source == 'englishSyn') {
      await saveKwpyCompletionProgress(userId, totalScore);
    }

    //提交学习记录
    saveuserreviewrecord();
  }

  Future<void> saveuserreviewrecord() async {
    var userInfo = ref.read(userInfoNotifierProvider);
    String userId = userInfo.userId;

    String studyPlanId = bookId;

    showLoading();
    var resp = await ApiRepository.saveuserreviewrecord(
            resourceId: resourceId,
            reviewModuleId: "47",
            userId: userId,
            studyPlanId: studyPlanId,
            resourceName: "课文配音")
        .whenComplete(() {
      dismissLoading();
    });

    if (resp.isSuccess && resp.isDataNotNull) {
      //??
      Logger.info("jsonStr===${resp.dataNotNull}");
    }
  }

//发音详情页面，播放原音和录音
  Future playOrigin(String filePath, DubbingVideoInfoModel model) async {
    if (isPlayingOrigin.value) {
      await recordController.pausePlay();
      isPlayingOrigin.value = false;
    } else {
      await _stopAllState();
      isPlayingOrigin.value = true;

      int? startMillseconds = _getMillseconds(model.startPoint);
      int? endMillseconds = _getMillseconds(model.endPoint);

      await recordController.playFromURIWithSeek(filePath, whenFinished: () {
        isPlayingOrigin.value = false;
      },
          seekDuration: startMillseconds != null
              ? Duration(milliseconds: startMillseconds)
              : null,
          endDuration: endMillseconds != null
              ? Duration(milliseconds: endMillseconds)
              : null);
    }
  }

  int? _getMillseconds(String? pointStr) {
    if (pointStr != null) {
      double? seconds = double.tryParse(pointStr);
      if (seconds != null) {
        int millseconds = (seconds * 1000).toInt();
        return millseconds;
      }
    }
    return null;
  }

  Future _stopAllState() async {
    if (isPlayingRecord.value) {
      isPlayingRecord.value = false;
      currentAnimateController.currentState?.stopAnimation();
    }
    if (isPlayingOrigin.value = true) {
      isPlayingOrigin.value = false;
    }
  }

  //回放
  Future playRecord(String resId, int index) async {
    currentAnimateController = playbackAnimateControllers[index];

    String audioResourceFolder =
        await TextDubbingFileUtil.audioRecordFolderPath(resourceId);
    String recordFilePath = "$audioResourceFolder/${resId}.wav";
    Logger.info("recordFilePath===$recordFilePath");
    if (isPlayingRecord.value) {
      await recordController.pausePlay();
      isPlayingRecord.value = false;
      currentAnimateController.currentState?.stopAnimation();
    } else {
      await _stopAllState();
      recordController.playFromURI(recordFilePath, whenFinished: () {
        isPlayingRecord.value = false;
        currentAnimateController.currentState?.stopAnimation();
      });

      isPlayingRecord.value = true;
      currentAnimateController.currentState?.playAnimation();
    }
  }

  //单词练习页面
  // 查询单词
  Future<TextDubbingWordModel> queryWordByName(String wordName) async {
    showLoading();
    var resp =
        await ApiRepository.findByName(wordName: wordName).whenComplete(() {
      dismissLoading();
    });

    if (resp.isSuccess && resp.isDataNotNull) {
      Logger.info("queryWordByName===${resp.dataNotNull}");
      word.value = resp.dataNotNull;
      isWordInfoFinish = true;
      return word.value;
    }
    word.value = word.value; // 触发监听
    isWordInfoFinish = true;
    return TextDubbingWordModel();
  }

  //单词练习页面，播放原音
  Future playWordOrigin() async {
    if (isPlayingOrigin.value) {
      await recordController.pausePlay();
      isPlayingOrigin.value = false;
    } else {
      await _stopAllState();
      isPlayingOrigin.value = true;

      String filePath = getWordAudioPath();
      await recordController.playFromURIWithSeek(
        filePath,
        whenFinished: () {
          isPlayingOrigin.value = false;
        },
      );
    }
  }

  String getWordAudioPath() {
    //英文
    List<ExpendList> expendList = word.value.expendList ?? [];
    for (ExpendList item in expendList) {
      if (item.type == "dcyp" && (item.filePath ?? "").length > 0) {
        return ImageUtil.getImageUrl(item.filePath ?? "");
      }
    }
    return "";
  }

  //单词练习页面，录音并评分
  Future<bool> doWordRecord(String wordName) async {
    //如果没有查到单词的时候，这里拿不到model
    String wordId = word.value.id ?? ("${resourceId}_$wordName");
    var model = getControlModel(wordId);
    if (!singSoundDubbingController.isRecording) {
      singSoundDubbingController.doPauseAll(model);
      //开始录音
      SingsoundResult? singSoundResult =
          await singSoundDubbingController.startRecord(
              10,
              word.value.name ?? "",
              await TextDubbingFileUtil.audioRecordFile(resourceId, wordId),
              model);

      //在此处设置体验免费测评的体验次数
      FreeCountUtil.increaseNewSpeechProfessionalFreeCount();
      //设置免费测评的次数
      freeCount.value = FreeCountUtil.getNewSpeechProfessionalFreeCount();

      Logger.info(
          "singSoundResult===${singSoundResult?.fluency?.overall ?? 0}");
      if (singSoundResult != null) {
        if ((singSoundResult.overall ?? 0) < 20) {
          showToast("此句录音得分较低，请重新录制!");
          wordResult.value = SingsoundResult();
        } else {
          wordResult.value = singSoundResult;
        }

        return true;
      }
    } else {
      Logger.info('singSoundDubbingController.doPauseAll');
      //停止录音
      singSoundDubbingController.doPauseAll(model);
    }
    return false;
  }

  //单词练习页面，播放录音
  void doWordReplay(String wordName) async {
    String wordId = word.value.id ?? ("${resourceId}_$wordName");
    var model = getControlModel(wordId);
    Logger.info("doWordReplay===${model}");
    if (wordResult.value.overall != null) {
      if (singSoundDubbingController.isReplaying) {
        singSoundDubbingController.doPauseAll(model);
      } else {
        singSoundDubbingController.doPauseAll(model);
        String filePath =
            await TextDubbingFileUtil.audioRecordFile(resourceId, wordId);
        singSoundDubbingController.startReplay(filePath, model);
      }
    } else {
      singSoundDubbingController.doPauseAll(model);
      showToast("请先录音");
    }
  }

  //专业测评页面，播放原音
  Future playEvaluateOrigin(
      String filePath, DubbingVideoInfoModel model) async {
    await playOrigin(filePath, model);
  }

  //专业测评页面，录音并评分
  Future<bool> doEvaluateRecord(DubbingVideoInfoModel videoInfo) async {
    var model = getControlModel(videoInfo.id ?? "");
    if (!singSoundDubbingController.isRecording) {
      singSoundDubbingController.doPauseAll(model);
      //开始录音
      SingsoundResult? singSoundResult =
          await singSoundDubbingController.startRecord(
              10,
              videoInfo.english ?? "",
              await TextDubbingFileUtil.audioRecordFile(
                  resourceId, videoInfo.id ?? ""),
              model);

      //在此处设置体验免费测评的体验次数
      FreeCountUtil.increaseNewSpeechProfessionalFreeCount();
      //设置免费测评的次数
      freeCount.value = FreeCountUtil.getNewSpeechProfessionalFreeCount();

      Logger.info(
          "singSoundResult===${singSoundResult?.fluency?.overall ?? 0}");
      if (singSoundResult != null) {
        if ((singSoundResult.overall ?? 0) < 20) {
          showToast("此句录音得分较低，请重新录制!");
          evaluateResult.value = SingsoundResult();
        } else {
          evaluateResult.value = singSoundResult;
          //此处更新句子
          if (singSoundResult.details != null) {
            sentenceDetails.value = singSoundResult.details!;
          }
        }

        return true;
      }
    } else {
      Logger.info('singSoundDubbingController.doPauseAll');
      //停止录音
      singSoundDubbingController.doPauseAll(model);
    }
    return false;
  }

  //专业测评页面，播放录音
  void doEvaluateReplay11(DubbingVideoInfoModel videoInfo) async {
    var model = getControlModel(videoInfo.id ?? "");
    if (evaluateResult.value.overall != null) {
      if (singSoundDubbingController.isReplaying) {
        singSoundDubbingController.doPauseAll(model);
        evaluateRepalyKey.currentState?.stopAnimation();
      } else {
        singSoundDubbingController.doPauseAll(model);
        String filePath = await TextDubbingFileUtil.audioRecordFile(
            resourceId, videoInfo.id ?? "");
        Logger.info("doWordReplay===${filePath}");
        singSoundDubbingController.startReplay(filePath, model);
      }
    } else {
      singSoundDubbingController.doPauseAll(model);
      evaluateRepalyKey.currentState?.stopAnimation();
      showToast("请先录音");
    }
  }

  Future doEvaluateReplay(DubbingVideoInfoModel videoInfo) async {
    String resId = videoInfo.id ?? "";
    // String audioResourceFolder = await TextDubbingFileUtil.audioRecordFolderPath(resourceId);
    String recordFilePath =
        await TextDubbingFileUtil.audioRecordFile(resourceId, resId);
    Logger.info("recordFilePath===$recordFilePath");
    if (isPlayingRecord.value) {
      await recordController.pausePlay();
      isPlayingRecord.value = false;
      evaluateRepalyKey.currentState?.stopAnimation();
    } else {
      await _stopAllState();
      recordController.playFromURI(recordFilePath, whenFinished: () {
        isPlayingRecord.value = false;
        evaluateRepalyKey.currentState?.stopAnimation();
      });

      isPlayingRecord.value = true;
      evaluateRepalyKey.currentState?.playAnimation();
    }
  }

  void setPhoneInfo(SingsoundResult result, Map<String, dynamic> answerBean) {
    List<Details> details = result.details ?? [];
    num lowerScore = -1;
    String lowerContent = "";
    for (Details d in details) {
      String? wordName = d.char;
      num score = d.score ?? 0;
      if (score < 60) {
        if (score < lowerScore) {
          lowerScore = score;
          lowerContent = wordName ?? "";
          setPhoneDetails(d.phone!, answerBean);
        } else {
          if (lowerScore == -1) {
            lowerScore = score;
            lowerContent = wordName ?? "";
            setPhoneDetails(d.phone!, answerBean);
          }
        }
      }
    }
    answerBean['problemWord'] = lowerContent;
  }

  void setPhoneDetails(List<Phone>? phone, Map<String, dynamic> answerBean) {
    String lowerPhone = "";
    if (phone != null && phone.length > 0) {
      num phoneLowerScore = -1;
      for (int j = 0; j < phone.length; j++) {
        num? phoneScore = phone[j].score ?? 0;
        String content = phone[j].char ?? "";
        //找到音素最低分
        if (phoneScore < 60) {
          if (phoneScore < phoneLowerScore) {
            phoneLowerScore = phoneScore;
            lowerPhone = content;
          } else {
            if (phoneLowerScore == -1) {
              phoneLowerScore = phoneScore;
              lowerPhone = content;
            }
          }
        }
      }
    }
    answerBean['phoneme'] = lowerPhone;
  }
}
