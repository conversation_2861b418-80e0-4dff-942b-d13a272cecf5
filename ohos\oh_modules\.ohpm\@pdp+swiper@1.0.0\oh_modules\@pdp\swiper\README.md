**@pdp/swiper**
***
***介绍***
@pdp/swiper 是针对系统Swiper控件做的书本仿真翻页效果，支持单页和双页模式

***

***如何安装***
```shell
ohpm install @pdp/swiper
```
***
***如何使用***

1、初始化 
```typescript
@State swiperBook: PdpBookSwiperTransition =
  new PdpBookSwiperTransition()
```
2、设置Swiper自定义动画
```typescript
Swiper() {
  ForEach(this.list, (data,index)=>{
    MyPage().backgroundColor(Color.White)
      .zIndex(this.swiperBook.swiper_zIndex(index))
      .rotate(this.swiperBook.swiper_rotate(index))
      .offset(this.swiperBook.swiper_offset(index))
      .scale(this.swiperBook.swiper_scale(index))
  })
}.displayCount(this.landscape ? 2 : 1, true).customContentTransition({
    timeout: 500,
    transition: (proxy: SwiperContentTransitionProxy)=>{
      this.swiperBook.swiper_transition(proxy, this.landscape)
    }
  })
```
