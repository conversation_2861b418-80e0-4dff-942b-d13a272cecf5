import { Any } from '@ohos/flutter_ohos';
import URLRequest from './URLRequest';
export default class NavigationAction {
    request: URLRequest;
    isForMainFrame: boolean;
    hasGesture: boolean;
    isRedirect: boolean;
    constructor(request: URLRequest, isForMainFrame: boolean, hasGesture: boolean, isRedirect: boolean);
    toMap(): Map<string, Any>;
    getRequest(): URLRequest;
    setRequest(request: URLRequest): void;
    getForMainFrame(): boolean;
    setForMainFrame(forMainFrame: boolean): void;
    isHasGesture(): boolean;
    setHasGesture(hasGesture: boolean): void;
    getRedirect(): boolean;
    setRedirect(redirect: boolean): void;
    equals(o: Any): boolean;
    toString(): string;
}
