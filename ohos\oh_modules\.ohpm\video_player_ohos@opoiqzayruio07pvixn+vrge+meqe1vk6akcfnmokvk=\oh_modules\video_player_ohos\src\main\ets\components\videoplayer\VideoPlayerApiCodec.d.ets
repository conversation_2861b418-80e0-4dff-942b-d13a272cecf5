import StandardMessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/StandardMessageCodec';
import { ByteBuffer } from '@ohos/flutter_ohos/src/main/ets/util/ByteBuffer';
export declare class VideoPlayerApiCodec extends StandardMessageCodec {
    static INSTANCE: VideoPlayerApiCodec;
    readValueOfType(type: number, buffer: ByteBuffer): ESObject;
    writeValue(stream: ByteBuffer, value: ESObject): ESObject;
}
