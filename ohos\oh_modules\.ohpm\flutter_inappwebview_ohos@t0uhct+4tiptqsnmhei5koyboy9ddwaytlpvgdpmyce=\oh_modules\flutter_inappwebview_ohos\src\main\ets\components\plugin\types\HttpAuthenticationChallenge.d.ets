import URLAuthenticationChallenge from './URLAuthenticationChallenge';
import URLCredential from './URLCredential';
import URLProtectionSpace from './URLProtectionSpace';
export default class HttpAuthenticationChallenge extends URLAuthenticationChallenge {
    private previousFailureCount;
    proposedCredential: URLCredential | null;
    constructor(protectionSpace: URLProtectionSpace, previousFailureCount: number, proposedCredential: URLCredential | null);
    toMap(): Promise<Map<string, any>>;
    getPreviousFailureCount(): number;
    setPreviousFailureCount(previousFailureCount: number): void;
    getProposedCredential(): URLCredential | null;
    setProposedCredential(proposedCredential: URLCredential | null): void;
    toString(): string;
}
