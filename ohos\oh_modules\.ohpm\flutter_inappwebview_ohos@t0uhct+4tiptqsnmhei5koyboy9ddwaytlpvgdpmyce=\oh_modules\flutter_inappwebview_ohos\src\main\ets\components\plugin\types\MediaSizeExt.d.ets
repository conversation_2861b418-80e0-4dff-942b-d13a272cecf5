export default class MediaSizeExt {
    private id;
    private label;
    private widthMils;
    private heightMils;
    constructor(id: string, label: string | null, widthMils: number, heightMils: number);
    static fromMap(map: Map<String, Object>): MediaSizeExt | null;
    toMap(): Map<String, Object>;
    getId(): string;
    setId(id: string): void;
    getLabel(): string;
    setLabel(label: string): void;
    getWidthMils(): number;
    setWidthMils(widthMils: number): void;
    getHeightMils(): number;
    setHeightMils(heightMils: number): void;
    equals(o: Object): boolean;
    hashCode(): number;
    toString(): string;
}
