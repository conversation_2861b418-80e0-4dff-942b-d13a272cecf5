-keep-property-name
__type__
appId
authResult
businessType
callbackAbility
code
country
errCode
errStr
errcode
errmsg
extData
extInfo
extMsg
extraData
fileUri
from
imageBase64
isOption1
lang
linkFeature
message
miniprogramType
nonAutomatic
nonceStr
openId
packageValue
partnerId
path
prepayId
qrcodebase64
qrcodelength
query
returnKey
scene
scope
sign
signType
state
tdiAuthBuffer
timeStamp
transaction
uri
url
userName
uuid
wx_code
wx_errcode
wx_media_message_description
wx_media_message_media_object
wx_media_message_media_object_type
wx_media_message_media_tag_name
wx_media_message_message_action
wx_media_message_message_ext
wx_media_message_thumb_data
wx_media_message_title
wx_miniprogram_disableforward
wx_miniprogram_extrainfo
wx_miniprogram_issecretmsg
wx_miniprogram_isupdatablemsg
wx_miniprogram_path
wx_miniprogram_type
wx_miniprogram_username
wx_miniprogram_wepageurl
wx_miniprogram_withshareticket
wx_text_object_text
wx_webpage_url
