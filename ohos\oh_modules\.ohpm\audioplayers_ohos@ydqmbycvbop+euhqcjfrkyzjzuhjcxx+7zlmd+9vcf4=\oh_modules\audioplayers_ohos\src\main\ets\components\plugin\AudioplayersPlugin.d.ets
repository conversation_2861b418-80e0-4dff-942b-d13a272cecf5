/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { FlutterPlugin, FlutterPluginBinding } from '@ohos/flutter_ohos/src/main/ets/embedding/engine/plugins/FlutterPlugin';
import { EventSink, StreamHandler } from '@ohos/flutter_ohos/src/main/ets/plugin/common/EventChannel';
import { AbilityAware, AbilityPluginBinding, EventChannel } from '@ohos/flutter_ohos';
import WrappedPlayer from './player/WrappedPlayer';
import audio from '@ohos.multimedia.audio';
export default class AudioplayersPlugin implements FlutterPlugin, AbilityAware, IUpdateCallback {
    private methods?;
    private globalMethods?;
    private globalEvents?;
    private context?;
    private binaryMessenger?;
    private soundPoolManager?;
    private players;
    private defaultAudioContext;
    private updateInterval;
    private abilityBinding?;
    private session?;
    private isStartContinuousTask;
    getUniqueClassName(): string;
    onAttachedToEngine(binding: FlutterPluginBinding): void;
    onDetachedFromEngine(binding: FlutterPluginBinding): void;
    private globalMethodCall;
    private methodCall;
    private getPlayer;
    getApplicationContext(): Context;
    getAudioManager(): audio.AudioManager;
    handleIsPlaying(): void;
    handleDuration(player: WrappedPlayer): void;
    handleComplete(player: WrappedPlayer): void;
    handlePrepared(player: WrappedPlayer, isPrepared: boolean): void;
    handleLog(player: WrappedPlayer, message: string): void;
    handleGlobalLog(message: string): void;
    handleError(player: WrappedPlayer, errorCode?: string, errorMessage?: string, errorDetails?: ESObject): void;
    handleGlobalError(errorCode?: string, errorMessage?: string, errorDetails?: ESObject): void;
    handleSeekComplete(player: WrappedPlayer): void;
    startUpdates(): void;
    stopUpdates(): void;
    private updateCallback;
    private getAudioContext;
    private getObjectValue;
    private error;
    onAttachedToAbility(binding: AbilityPluginBinding): void;
    onDetachedFromAbility(): void;
    startContinuousTask(): Promise<void>;
    stopContinuousTask(): Promise<void>;
}
export declare class EventHandler implements StreamHandler {
    private eventChannel?;
    private eventSink;
    constructor(eventChannel: EventChannel);
    onListen(args: ESObject, events: EventSink): void;
    onCancel(args: ESObject): void;
    success(method: string, args?: Map<string, ESObject>): void;
    error(errorCode?: string, errorMessage?: string, errorDetails?: ESObject): void;
    dispose(): void;
}
interface IUpdateCallback {
    stopUpdates(): void;
    startUpdates(): void;
}
export {};
