import { Any } from '@ohos/flutter_ohos';
export default class GeolocationPermissionShowPromptResponse {
    private origin;
    allow: boolean;
    retain: boolean;
    constructor(origin: string, allow: boolean, retain: boolean);
    static fromMap(map: Map<string, Any>): GeolocationPermissionShowPromptResponse | null;
    getOrigin(): string;
    setOrigin(origin: string): void;
    isAllow(): boolean;
    setAllow(allow: boolean): void;
    isRetain(): boolean;
    setRetain(retain: boolean): void;
    toString(): string;
}
