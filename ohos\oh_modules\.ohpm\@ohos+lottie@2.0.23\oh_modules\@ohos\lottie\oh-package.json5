{"types": "index.d.ts", "keywords": ["OpenHarmony", "HarmonyOS", "<PERSON><PERSON>"], "author": "ohos_tpc", "ohos": {"org": "opensource"}, "description": "lottie是一个适用于OpenHarmony的动画库，它可以使用Bodymovin解析以json格式导出的Adobe After Effects动画，并在移动设备上进行本地渲染", "main": "src/main/js/modules/full.js", "repository": "https://gitcode.com/openharmony-tpc/lottieArkTS.git", "type": "module", "version": "2.0.23", "tags": ["Animation"], "dependencies": {}, "license": "MIT", "devDependencies": {}, "name": "@ohos/lottie", "metadata": {"sourceRoots": ["./src/main"], "debug": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "OpenHarmony", "obfuscated": false}