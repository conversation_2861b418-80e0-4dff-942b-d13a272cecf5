import media from '@ohos.multimedia.media';
import resourceManager from '@ohos.resourceManager';
import { SurfaceTextureEntry } from '@ohos/flutter_ohos/src/main/ets/view/TextureRegistry';
import { PlayerModel } from './PlayerModel';
import { EventChannel } from '@ohos/flutter_ohos';
export declare class VideoPlayer {
    private avPlayer;
    playerModel: PlayerModel | null;
    private duration;
    private status;
    private loop;
    private index;
    private url?;
    private iUrl;
    private surfaceId;
    private seekTime;
    private positionX;
    private positionY;
    private textureEntry;
    private eventChannel;
    private eventSink;
    private interruptMode;
    private fd;
    private headers;
    constructor(playerModel: PlayerModel, textureEntry: SurfaceTextureEntry, url: resourceManager.RawFileDescriptor | null, iUrl: string | null, eventChannel: EventChannel, AudioFocus: Boolean, headers: Record<string, string> | null);
    /**
     * Creates a videoPlayer object.
     */
    createAVPlayer(): Promise<void>;
    /**
     * AVPlayer binding event.
     */
    bindState(): Promise<void>;
    /**
     * Release the video player.
     */
    release(): void;
    play(): void;
    /**
     * Pause Playing.
     */
    pause(): void;
    seekTo(position: number): void;
    getPosition(): number;
    /**
     * Playback mode. The options are as follows: true: playing a single video; false: playing a cyclic video.
     */
    setLoop(): void;
    setLooping(isLooping: boolean): void;
    setVolume(volume: number): void;
    /**
     * Set the playback speed.
     *
     * @param playSpeed Current playback speed.
     */
    setSpeed(playSpeed: number): void;
    /**
     * Previous video.
     */
    previousVideo(): void;
    /**
     * Next video.
     */
    nextVideo(): void;
    /**
     * Switching Between Video Play and Pause.
     */
    switchPlayOrPause(): void;
    /**
     * Slide the progress bar to set the playback progress.
     *
     * @param value Value of the slider component.
     * @param mode Slider component change event.
     */
    setSeekTime(value: number, mode: SliderChangeMode): void;
    /**
     * Setting the brightness.
     */
    setBright(): void;
    /**
     * Obtains the current video playing status.
     */
    getStatus(): number;
    /**
     * Initialization progress bar.
     *
     * @param time Current video playback time.
     */
    initProgress(time: number): void;
    /**
     * Reset progress bar data.
     */
    resetProgress(): void;
    /**
     * Volume gesture method onActionStart.
     *
     * @param event Gesture event.
     */
    onVolumeActionStart(event?: GestureEvent): void;
    /**
     * Bright gesture method onActionStart.
     *
     * @param event Gesture event.
     */
    onBrightActionStart(event?: GestureEvent): void;
    /**
     * Gesture method onActionUpdate.
     *
     * @param event Gesture event.
     */
    onVolumeActionUpdate(event?: GestureEvent): void;
    /**
     * Gesture method onActionUpdate.
     *
     * @param event Gesture event.
     */
    onBrightActionUpdate(event?: GestureEvent): void;
    /**
     * Gesture method onActionEnd.
     */
    onActionEnd(): void;
    /**
     * Sets whether the screen is a constant based on the playback status.
     */
    watchStatus(): void;
    /**
     * Sets the playback page size based on the video size.
     */
    setVideoSize(): void;
    /**
     * An error is reported during network video playback.
     */
    playError(): void;
    sendInitialized(): void;
    sendCompleted(): void;
    sendBufferingUpdate(infoType: media.BufferingInfoType, bufferingPosition: number): void;
    sendError(error: Object): void;
    getIUri(): string;
}
