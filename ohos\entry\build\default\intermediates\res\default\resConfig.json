{"configPath": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\process_profile\\default\\module.json", "packageName": "com.yyb.hm", "output": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default", "moduleNames": "entry", "ResourceTable": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\generated\\r\\default\\ResourceTable.h"], "applicationResource": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\AppScope\\resources", "moduleResources": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\src\\main\\resources"], "dependencies": ["D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audioplayers_ohos@ydqmbycvbop+euhqcjfrkyzjzuhjcxx+7zlmd+9vcf4=\\oh_modules\\audioplayers_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_inappwebview_ohos@t0uhct+4tiptqsnmhei5koyboy9ddwaytlpvgdpmyce=\\oh_modules\\flutter_inappwebview_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\video_player_ohos@opoiqzayruio07pvixn+vrge+meqe1vk6akcfnmokvk=\\oh_modules\\video_player_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\permission_handler_ohos@y5c4tttcyqjngchkdn5yjppchbdieuwhltbbukzgm3m=\\oh_modules\\permission_handler_ohos\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\package_info_plus@lspkyseckpn2rwqtcnovkykvut+ntcctdr7s+1gvtwe=\\oh_modules\\package_info_plus\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\flutter_sound@529awjslwh3qcnziil+ezovduw38wiekxlbgbmv+nsi=\\oh_modules\\flutter_sound\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\connectivity_plus@9gb+usrgymvla7djn9vciadb2x4gtm6j5vcvfurtfiu=\\oh_modules\\connectivity_plus\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\mobile_scanner@lmqqpmehu9aeh+rlkm6sviq7gun8foewdubkv2+cwzg=\\oh_modules\\mobile_scanner\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+book@kwetdlm8p3fehla1xud5h+ejryzid8z0alb9exoy9vg=\\oh_modules\\@pdp\\book\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\singsound@3noxo1+ldk0a5+ax9utefojjt1ehalgj+carzztjwxy=\\oh_modules\\singsound\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\device_info_plus@ka00ia+uz3+4hmmyhemhiunbbkncadf84p8oqt3ceym=\\oh_modules\\device_info_plus\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\audio_session@qxxez7jmqfxotptpn+chz3pxnxcilrjzumuiujrkcj0=\\oh_modules\\audio_session\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+crypto-js@2.0.4\\oh_modules\\@ohos\\crypto-js\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+swiper@1.0.0\\oh_modules\\@pdp\\swiper\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@pdp+evaluation@1.1.3\\oh_modules\\@pdp\\evaluation\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\bigdata@mptnw0wkm5spww48ifee6omoheeuqmqsa3pwhpz9qpk=\\oh_modules\\bigdata\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@ohos+mp4parser@2.0.3-rc.1\\oh_modules\\@ohos\\mp4parser\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+common@1.1.3\\oh_modules\\@umeng\\common\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\oh_modules\\.ohpm\\@umeng+analytics@1.2.4\\oh_modules\\@umeng\\analytics\\src\\main\\resources", "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\resource_str"], "iconCheck": true, "compression": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\opt-compression.json", "ids": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\ids_map", "definedIds": "D:\\workspace\\dev\\harmonyOs\\yyb_flutter\\ohos\\entry\\build\\default\\intermediates\\res\\default\\ids_map\\id_defined.json", "definedSysIds": "D:\\softs\\DevEco\\DevEco Studio\\sdk\\default\\hms\\toolchains\\id_defined.json"}