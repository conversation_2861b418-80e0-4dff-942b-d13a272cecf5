import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Disposable } from '../types/Disposable';
import PullToRefreshChannelDelegate from './PullToRefreshChannelDelegate';
import PullToRefreshSettings from './PullToRefreshSettings';
import InAppWebView from '../webview/in_app_webview/InAppWebView';
export default class PullToRefreshLayout implements Disposable {
    channelDelegate: PullToRefreshChannelDelegate | null;
    settings: PullToRefreshSettings;
    enabled: boolean;
    webView: InAppWebView | null;
    constructor(plugin: InAppWebViewFlutterPlugin | null, id: Object | null, settings: PullToRefreshSettings);
    setEnabled(enable: boolean): void;
    isEnabled(): boolean;
    setRefreshing(refreshing: boolean): void;
    isRefreshing(): boolean;
    onRefresh(): void;
    addView(webView: InAppWebView): void;
    prepare(): void;
    dispose(): void;
}
