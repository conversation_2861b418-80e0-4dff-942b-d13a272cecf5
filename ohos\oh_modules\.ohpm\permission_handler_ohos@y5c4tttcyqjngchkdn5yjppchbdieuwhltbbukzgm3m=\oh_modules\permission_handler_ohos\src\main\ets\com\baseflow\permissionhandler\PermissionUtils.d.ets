import ArrayList from '@ohos.util.ArrayList';
import { Callback } from '@ohos.base';
export default class PermissionUtils {
    static parseOhosName(permission: String): number;
    static hasPermissionInManifest(confirmedPermission: ArrayList<string>, permission: string, callback: Callback<boolean>): void;
    static getRequestPermission(permissions: ArrayList<string>, callback: Callback<ArrayList<string>>): void;
    static getManifestNames(permission: number, callback: ESObject): void;
    static toPermissionStatus(authResult: number): number;
    static updatePermissionShouldShowStatus(permission: number): void;
}
