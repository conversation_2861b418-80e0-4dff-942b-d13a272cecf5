{"app": {"bundleName": "com.jinxin.readDemo", "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50000012, "targetAPIVersion": 50001013, "apiReleaseType": "Release", "compileSdkVersion": "*********", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app"}, "module": {"name": "swiper", "type": "har", "deviceTypes": ["default", "tablet", "2in1"], "packageName": "@pdp/swiper", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}