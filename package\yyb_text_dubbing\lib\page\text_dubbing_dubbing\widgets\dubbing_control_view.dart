import 'dart:async';
import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:flutter/material.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/utils/business/sing_sound_dubbing_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/animation/image_switch_animation.dart';
import 'package:yyb_text_dubbing/model/http/dubbing_video_info_model.dart';
import 'package:yyb_text_dubbing/providers/text_dubbing_dubbing_provider.dart';
import 'package:yyb_text_dubbing/src/utils/asset_utils.dart';
import 'package:yyb_text_dubbing/src/generated/assets.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/controllers/text_dubbing_record_provider.dart';

class DubbingControlView extends StatelessWidget {
  final TextDubbingDubbingProvider notifier;
  final TextDubbingRecordProvider controller;
  const DubbingControlView({required this.notifier, required this.controller});

  @override
  Widget build(BuildContext context) {
    DubbingVideoInfoModel model = notifier.currentDubbing.value;
    ControlNotifierModel controlNotifierModel =
        notifier.getControlModel(model.id ?? "");
    return Container(
      width: 260.r,
      // decoration: BoxDecoration(color: Colors.blue),
      child: TextDubbingPlayActionRow(
        notifier: notifier,
        controller: controller,
      ),
    );
  }

  Widget _funcItem(String title, double w, VoidCallback itemOnTap) {
    return GestureDetector(
      onTap: itemOnTap,
      child: Column(
        children: [
          Container(
            width: 50.r,
            height: 40.r,
            padding: EdgeInsets.all(w),
            child: Image(
              image: AssetImage(
                  AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon1)),
              width: w,
            ),
          ),
          SizedBox(
            height: 5.r,
          ),
          Text(
            title,
            style: TextStyle(
                color: ThemeConfig.currentTheme.colorTextPrimary1,
                fontSize: 14.sp),
          ),
        ],
      ),
    );
  }
}

class TextDubbingPlayActionRow extends StatefulWidget {
  final TextDubbingDubbingProvider notifier;
  final TextDubbingRecordProvider controller;
  // final VoidCallback itemOnTap;
  const TextDubbingPlayActionRow(
      {required this.notifier, required this.controller});

  @override
  State<TextDubbingPlayActionRow> createState() =>
      _TextDubbingPlayActionRowState();
}

class _TextDubbingPlayActionRowState extends State<TextDubbingPlayActionRow> {
  bool isPlaying = false;
  bool isRecording = false;
  bool isReplaying = false;
  bool hasRecord = false;
  double progress = 0;
  Timer? _timer;
  int _current = 0;
  int _total = 0; //毫秒数
  late ControlNotifierModel controlNotifierModel;

  @override
  void initState() {
    super.initState();

    DubbingVideoInfoModel model = widget.notifier.currentDubbing.value;
    _total = (double.parse(model.endPoint ?? "0") -
            double.parse(model.startPoint ?? "0"))
        .round();
    _total = _total * 10;

    controlNotifierModel = widget.notifier.getControlModel(model.id ?? "");

    hasRecord = widget.notifier.dubbingResultMap
        .containsKey(widget.notifier.currentDubbing.value.id ?? "");

    widget.controller.mVideoPlayerController.videoController?.addListener(() {
      bool res = widget
          .controller.mVideoPlayerController.videoController!.value.isPlaying;
      isPlaying = res;
      if (mounted) {
        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        //进度条
        Center(
          child: _lineProgressIndicator(),
        ),
        SizedBox(
          height: 15.r,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Visibility(
              visible: !isRecording,
              child: _originDubbingItem(),
            ),
            SizedBox(
              width: 15.r,
            ),
            _recordDubbingItem(),
            SizedBox(
              width: 15.r,
            ),
            Visibility(
              visible: !isRecording,
              child: _replayDubbingItem(),
            ),
          ],
        ),
      ],
    );
  }

  Widget _lineProgressIndicator() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Row(
          children: [
            Container(
              width: 230.r,
              height: 6.r,
              decoration: BoxDecoration(
                color: ThemeConfig.currentTheme.colorDivider2,
                borderRadius: BorderRadius.all(Radius.circular(3.r)),
              ),
            ),
            SizedBox(
              width: 5.r,
            ),
            ValueListenableBuilder(
                valueListenable: widget.notifier.currentDubbing,
                builder: (context, value, child) {
                  int totalSecond = (double.parse(value.endPoint ?? "0") -
                          double.parse(value.startPoint ?? "0"))
                      .round();
                  return Text(
                    "${totalSecond}s",
                    style: TextStyle(
                        color: ThemeConfig.currentTheme.colorTextPrimary1,
                        fontSize: 14.sp),
                  );
                }),
          ],
        ),
        Positioned(
          left: 0,
          child: ValueListenableBuilder(
            valueListenable: widget.notifier.currentDubbing,
            builder: (context, value, child) {
              return Container(
                width: 230.r * progress,
                height: 6.r,
                decoration: BoxDecoration(
                  color: ThemeConfig.currentTheme.colorBgOrange,
                  borderRadius: BorderRadius.all(Radius.circular(3.r)),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _originDubbingItem() {
    return GestureDetector(
      onTap: () {
        if (!isPlaying) {
          widget.controller.doPlay();
        }
      },
      child: Column(
        children: [
          Container(
            width: 50.r,
            height: 40.r,
            padding: EdgeInsets.all(5.r),
            child: Image(
              image: AssetImage(AssetsUtils.wrapAsset(isPlaying
                  ? Assets.imagesTchSpokenPlaying
                  : Assets.imagesTchSpokenNotPlay)),
              width: 5.r,
            ),
          ),
          SizedBox(
            height: 5.r,
          ),
          Text(
            "原音",
            style: TextStyle(
                color: ThemeConfig.currentTheme.colorTextPrimary1,
                fontSize: 14.sp),
          ),
        ],
      ),
    );
  }

  Widget _recordDubbingItem() {
    return GestureDetector(
      onTap: () {
        if (isRecording) {
          //停止录音
          isRecording = false;
          _timer?.cancel();
        } else {
          isRecording = true;

          if (isPlaying) {
            //暂停
            widget.controller.mVideoPlayerController.videoController?.pause();
          }
          if (isRecording) {
            progress = 0;
            _current = 0;

            //进度条
            _timer = Timer.periodic(Duration(milliseconds: 100), (timer) {
              _current++;
              progress = _current / _total;
              if (progress > 1) progress = 1;
              Logger.info("current===$_current,,,$progress");
              if (_current == _total) {
                _timer?.cancel();
                isRecording = false;
                _current = 0;
                //更新按钮状态
                hasRecord = widget.notifier.dubbingResultMap
                    .containsKey(widget.notifier.currentDubbing.value.id ?? "");
              }
              setState(() {});
            });
          }
        }

        widget.notifier.doRecord(
            (_total * 0.1).toInt(), widget.notifier.currentDubbing.value);

        setState(() {});
      },
      child: Column(
        children: [
          Container(
            width: 50.r,
            height: 40.r,
            padding: EdgeInsets.all(5.r),
            child: Image(
              image: AssetImage(
                  AssetsUtils.wrapAsset(Assets.imagesTchSpokenRecordIcon)),
              width: 5.r,
            ),
          ),
          SizedBox(
            height: 5.r,
          ),
          Text(
            isRecording ? "点击停止" : "点击录音",
            style: TextStyle(
                color: ThemeConfig.currentTheme.colorTextPrimary1,
                fontSize: 14.sp),
          ),
        ],
      ),
    );
  }

  Widget _replayDubbingItem() {
    ControlNotifierModel controlNotifierModel = widget.notifier
        .getControlModel(widget.notifier.currentDubbing.value.id ?? "");
    return GestureDetector(
      onTap: () {
        Logger.info(
            "=======widget.notifier.currentDubbing.value.id:${widget.notifier.currentDubbing.value.id}, widget.notifier.dubbingResultMap.containsKey(widget.notifier.currentDubbing.value.id):${widget.notifier.dubbingResultMap.containsKey(widget.notifier.currentDubbing.value.id)}");
        if (!widget.notifier.dubbingResultMap
            .containsKey(widget.notifier.currentDubbing.value.id)) {
          showToast("请先录音");
          return;
        }
        isReplaying = !isReplaying;
        if (isReplaying) {
          widget.notifier.doReplay(
            widget.notifier.currentDubbing.value,
          );
        }

        Future.delayed(Duration(milliseconds: _total * 100), () {
          isReplaying = widget.notifier.singSoundDubbingController.isReplaying;
          setState(() {});
        });
        setState(() {});
      },
      child: Column(
        children: [
          Container(
            width: 50.r,
            height: 40.r,
            padding: EdgeInsets.all(5.r),
            child: isReplaying
                ? ImageSwitchAnimation(
                    key: controlNotifierModel.replayKey,
                    images: [
                      AssetsUtils.wrapAsset(
                        Assets.imagesTchSpokenMyRecord1,
                      ),
                      AssetsUtils.wrapAsset(
                        Assets.imagesTchSpokenMyRecord2,
                      ),
                      AssetsUtils.wrapAsset(
                        Assets.imagesTchSpokenMyRecord3,
                      ),
                      AssetsUtils.wrapAsset(
                        Assets.imagesTchSpokenMyRecord4,
                      ),
                    ],
                    autoPlay: true,
                    width: 40.r,
                    height: 40.r)
                : Image(
                    image: AssetImage(AssetsUtils.wrapAsset(hasRecord
                        ? Assets.imagesTchSpokenRecordDefault
                        : Assets.imagesTchSpokenNoRecord)),
                    width: 5.r,
                  ),
          ),
          SizedBox(
            height: 5.r,
          ),
          Text(
            "原音",
            style: TextStyle(
                color: ThemeConfig.currentTheme.colorTextPrimary1,
                fontSize: 14.sp),
          ),
        ],
      ),
    );
  }
}

class _OriginDubbingItem extends StatefulWidget {
  final TextDubbingDubbingProvider notifier;
  final TextDubbingRecordProvider controller;
  // final VoidCallback itemOnTap;
  const _OriginDubbingItem({required this.notifier, required this.controller});

  @override
  State<_OriginDubbingItem> createState() => _OriginDubbingItemState();
}

class _OriginDubbingItemState extends State<_OriginDubbingItem> {
  bool isPlaying = false;

  @override
  void initState() {
    super.initState();

    widget.controller.mVideoPlayerController.videoController?.addListener(() {
      bool res = widget
          .controller.mVideoPlayerController.videoController!.value.isPlaying;
      isPlaying = res;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!isPlaying) {
          widget.controller.doPlay();
        }
      },
      child: Column(
        children: [
          Container(
            width: 50.r,
            height: 40.r,
            padding: EdgeInsets.all(5.r),
            child: Image(
              image: AssetImage(AssetsUtils.wrapAsset(isPlaying
                  ? Assets.imagesTchSpokenPlaying
                  : Assets.imagesTchSpokenNotPlay)),
              width: 5.r,
            ),
          ),
          SizedBox(
            height: 5.r,
          ),
          Text(
            "原音",
            style: TextStyle(
                color: ThemeConfig.currentTheme.colorTextPrimary1,
                fontSize: 14.sp),
          ),
        ],
      ),
    );
  }
}

class _RecordDubbingItem extends StatefulWidget {
  final TextDubbingDubbingProvider notifier;
  final TextDubbingRecordProvider controller;
  // final VoidCallback itemOnTap;
  const _RecordDubbingItem({required this.notifier, required this.controller});

  @override
  State<_RecordDubbingItem> createState() => _RecordDubbingItemState();
}

class _RecordDubbingItemState extends State<_RecordDubbingItem> {
  bool isRecording = false;

  @override
  void initState() {
    super.initState();

    widget.controller.mVideoPlayerController.videoController?.addListener(() {
      bool res = widget
          .controller.mVideoPlayerController.videoController!.value.isPlaying;
      isRecording = res;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!isRecording) {
          widget.controller.doPlay();
        }
      },
      child: Column(
        children: [
          Container(
            width: 50.r,
            height: 40.r,
            padding: EdgeInsets.all(5.r),
            child: Image(
              image: AssetImage(
                  AssetsUtils.wrapAsset(Assets.imagesTchSpokenRecordIcon)),
              width: 5.r,
            ),
          ),
          SizedBox(
            height: 5.r,
          ),
          Text(
            isRecording ? "点击停止" : "点击录音",
            style: TextStyle(
                color: ThemeConfig.currentTheme.colorTextPrimary1,
                fontSize: 14.sp),
          ),
        ],
      ),
    );
  }
}
