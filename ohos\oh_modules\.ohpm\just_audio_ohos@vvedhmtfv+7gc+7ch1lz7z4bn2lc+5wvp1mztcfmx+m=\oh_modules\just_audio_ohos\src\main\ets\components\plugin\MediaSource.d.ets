/**
 * Copyright (C) 2024 Huawei Device Co., Ltd.
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to deal
 * in the Software without restriction, including without limitation the rights
 * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
 * copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in all
 * copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
 * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
 * SOFTWARE.
 * */
import media from "@ohos.multimedia.media";
import { LoopMode } from './MediaAvPlayer';
export default class MediaSource implements media.MediaSource {
    header: Record<string, string> | null;
    id: string;
    uri: string;
    type: string;
    startUs: number;
    endUs: number;
    isAtomic: boolean;
    useLazyPreparation: boolean;
    musicPlayMode: LoopMode;
    shuffleOrder: number[];
    childMediaSource: MediaSource[];
    mimeType: media.AVMimeTypes;
    setStartUs(startUs: number): MediaSource;
    setEndUs(endUs: number): MediaSource;
    getStartUs(): number;
    getEndUs(): number;
    setMimeType(mimeType: media.AVMimeTypes): MediaSource;
    setMediaResourceLoaderDelegate(): void;
    getMimeType(): media.AVMimeTypes;
    setHeader(header: Record<string, string>): void;
    getHeader(): Record<string, string> | null;
    setId(id: string): MediaSource;
    getId(): string;
    setUri(uri: string): MediaSource;
    getUri(): string;
    setType(type: string): MediaSource;
    getType(): string;
    setAtomic(isAtomic: boolean): MediaSource;
    setMusicPlayMode(musicPlayMode: LoopMode): MediaSource;
    getMusicPlayMode(): LoopMode;
    IsAtomic(): boolean;
    setUseLazyPreparation(useLazyPreparation: boolean): MediaSource;
    IsUseLazyPreparation(): boolean;
    setShuffleOrder(shuffleOrder: number[]): MediaSource;
    getShuffleOrder(): number[];
    setChildMediaSource(childMediaSource: MediaSource[]): MediaSource;
    getChildMediaSource(): MediaSource[];
}
