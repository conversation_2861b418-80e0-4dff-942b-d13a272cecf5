{"flutter_sound|flutter_sound|1.0.0|build/default/generated/profile/default/ModuleInfo.js": {"version": 3, "file": "ModuleInfo.ts", "sources": ["ohos/build/default/generated/profile/default/ModuleInfo.ts"], "names": [], "mappings": "AACA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;AACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAC,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAwB,EAAE,CAAA,CAAA;", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|index.ts": {"version": 3, "file": "index.ets", "sourceRoot": "", "sources": ["ohos/index.ets"], "names": [], "mappings": "OAeO,kBAAkB;AACzB,eAAe,kBAAkB,CAAC", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/player/FlutterAudioRenderer.ts": {"version": 3, "file": "FlutterAudioRenderer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/player/FlutterAudioRenderer.ets"], "names": [], "mappings": "OAeO,KAAK;OAEL,EAAoB;cAAd,YAAY;cAEhB,OAAO,QAAQ,WAAW;OAC5B,EAAE,GAAG,EAAE;OACP,EAAW,cAAc,EAAqB;cAC5C,0BAA0B,QAAQ,sCAAsC;AAKjF,MAAM,GAAG,GAAG,sBAAsB,CAAC;AAEnC,MAAM,OAAO,oBAAqB,YAAW,OAAO;IAEhD,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,iBAAiB,CAAC,GAAG;QACpE,IAAI,EAAE,KAAK,CAAC,iBAAiB,CAAC,gBAAgB;QAC9C,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,MAAM,EAAE,KAAK,CAAC,iBAAiB,CAAC,kBAAkB;QAClD,MAAM,EAAE,KAAK,CAAC,iBAAiB,CAAC,kBAAkB;KACrD,CAAA;IAED,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG;QAC5D,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACjC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACjC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACjC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;KACpC,CAAA;IAED,OAAO,CAAC,WAAW,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IACvD,OAAO,CAAC,QAAQ,EAAE,0BAA0B,GAAG,IAAI,GAAG,IAAI,CAAC;IAE3D,OAAO,CAAC,cAAc,EAAE,MAAM,GAAG,CAAC,CAAC;IACnC,OAAO,CAAC,aAAa,EAAE,MAAM,GAAG,CAAC,CAAC;IAElC,OAAO,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC;IAC/B,OAAO,CAAC,gBAAgB,EAAE,MAAM,GAAG,CAAC,CAAC;IAErC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IAEpC,OAAO,CAAC,iBAAiB,GAAG,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;QAChD,IAAI,OAAO,EAAE,YAAY,GAAG;YACxB,MAAM,EAAE,IAAI,CAAC,UAAU;YACvB,MAAM,EAAE,MAAM,CAAC,UAAU;SAC5B,CAAC;QAEF,IAAI;YACA,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,EAAE,EAAG,MAAM,EAAE,OAAO,CAAC,CAAC;YAC7C,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAC;YACrC,OAAO,KAAK,CAAC,uBAAuB,CAAC,KAAK,CAAC;SAC9C;QAAC,OAAO,KAAK,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sBAAsB,GAAG,KAAK,CAAC,CAAC;SAC9C;QACD,OAAO,KAAK,CAAC,uBAAuB,CAAC,OAAO,CAAC;IACjD,CAAC,CAAA;IAED,YAAY,EAAE,EAAE,0BAA0B;QACtC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EACnE,UAAU,EAAC,MAAM,EAAE,qBAAqB,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;QAEjE,IAAI,eAAe,EAAE,KAAK,CAAC,eAAe,GAAG;YACzC,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;YACvD,QAAQ,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;YACtC,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC,mBAAmB;YACzD,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,OAAO;SAClE,CAAC;QAEF,IAAI,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,GAAG;YAC7C,KAAK,EAAE,KAAK,CAAC,WAAW,CAAC,kBAAkB;YAC3C,aAAa,EAAE,CAAC,CAAC,UAAU;SAC9B,CAAC;QAEF,IAAI,kBAAkB,EAAE,KAAK,CAAC,oBAAoB,GAAG;YACjD,UAAU,EAAE,eAAe;YAC3B,YAAY,EAAE,iBAAiB;SAClC,CAAC;QAEF,IAAI,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;QAEvE,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,IAAI,IAAI,SAAS,EAAE;gBACnB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;gBACpB,IAAI,CAAC,gBAAgB,GAAG,UAAU,CAAC;gBACnC,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACrD,CAAC,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC;aACrF;YAED,IAAI,CAAC,iBAAiB,EAAE,CAAC;SAC5B;QACD,OAAO;IACX,CAAC;IAED,KAAK,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO;SACV;QAED,IAAI;YACA,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,CAAC;YAC9B,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;SACpC;QAAC,OAAO,GAAG,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mCAAmC,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACzE;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC5B,CAAC;IAED,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;QAE9B,MAAM,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;QAChC,OAAO;IACX,CAAC;IAED,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;QAE/B,MAAM,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE,CAAC;QAChC,OAAO;IACX,CAAC;IAED,MAAM,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;QAE9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mBAAmB,CAAC,CAAC;QAChC,OAAO;IACX,CAAC;IAED,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;QAE3B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sBAAsB,CAAC,CAAC;QACnC,OAAO;IACX,CAAC;IAED,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;QAEzB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClC,OAAO;IACX,CAAC;IAED,IAAI,QAAQ,IAAI,MAAM;QAElB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,QAAQ,IAAI,MAAM;QAElB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClC,OAAO,CAAC,CAAC;IACb,CAAC;IAED,IAAI,KAAK,IAAI,cAAc;QAEvB,QAAQ,IAAI,CAAC,WAAW,EAAE,KAAK,EAAE;YAC7B,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;YACpC,KAAK,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC;YAChC,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC;YACrC,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;YACpC,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc;gBAChC,OAAO,cAAc,CAAC,iBAAiB,CAAC;YAC5C,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;gBAC/B,OAAO,cAAc,CAAC,iBAAiB,CAAC;YAC5C,KAAK,KAAK,CAAC,UAAU,CAAC,YAAY;gBAC9B,OAAO,cAAc,CAAC,gBAAgB,CAAC;YAC3C;gBACI,OAAO,cAAc,CAAC,iBAAiB,CAAC;SAC/C;IACL,CAAC;IAED,IAAI,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM;QAC3B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,iBAAiB,CAAC,CAAC;QAC9B,OAAO,CAAC,CAAC;IACb,CAAC;IAED,SAAS,IAAI,OAAO;QAEhB,OAAO,IAAI,CAAC,WAAW,EAAE,KAAK,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;IACtE,CAAC;IAED,OAAO,CAAC,iBAAiB,IAAI,IAAI;QAC7B,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE;YAClE,QAAQ,KAAK,EAAE;gBACX,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;oBAC/B,MAAM;gBACV,KAAK,KAAK,CAAC,UAAU,CAAC,SAAS;oBAC3B,MAAM;gBACV,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc;oBAChC,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,MAAM,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,EAAE,CAAC;gBACpC,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;oBAC/B,IAAI,CAAC,eAAe,EAAE,CAAC;oBACvB,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;oBAC7C,MAAM;gBACV,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;oBAC/B,MAAM;gBACV,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc;oBAChC,MAAM;gBACV,KAAK,KAAK,CAAC,UAAU,CAAC,YAAY;oBAC9B,MAAM;gBACV;oBACI,MAAM;aACb;YACD,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC,yCAAyC,CAAC,CAAC;aAC3D;YACD,IAAI,KAAK,IAAI,CAAC,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC,wCAAwC,CAAC,CAAC;aAC1D;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,eAAe,IAAI,OAAO,CAAC,IAAI,CAAC;QAC1C,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,WAAW,EAAE,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;SACpD;QAED,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,IAAI,IAAI,CAAC,aAAa,IAAI,GAAG,EAAE;YACzD,IAAI,CAAC,WAAW,EAAE,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;SAClD;IACL,CAAC;CACJ", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/player/FlutterAVPlayer.ts": {"version": 3, "file": "FlutterAVPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/player/FlutterAVPlayer.ets"], "names": [], "mappings": "OAcO,EAAW,cAAc,EAAE,iBAAiB,EAAE;cAC5C,OAAO,QAAQ,WAAW;OAC1B,KAAK;cACL,0BAA0B,QAAQ,sCAAsC;OAC1E,EAAE,GAAG,EAAE;cACL,aAAa,IAAb,aAAa;OACf,EAAE;AAET,MAAM,GAAG,GAAG,iBAAiB,CAAC;AAC9B,MAAM,OAAO,eAAgB,YAAW,OAAO;IAC7C,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,GAAG,IAAI,GAAG,IAAI,CAAC;IAC/C,OAAO,CAAC,QAAQ,EAAE,0BAA0B,GAAG,IAAI,GAAG,IAAI,CAAC;IAC3D,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IACzC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;IAClF,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAC5B,OAAO,CAAC,OAAO,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,oBAAoB,EAAE,MAAM,GAAG,IAAI,GAAG,IAAI,CAAC;IAEnD,YAAY,EAAE,EAAE,0BAA0B;QACxC,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,KAAK,IAAI,cAAc;QACzB,QAAQ,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE;YAC5B,KAAK,MAAM,CAAC;YACZ,KAAK,aAAa,CAAC;YACnB,KAAK,UAAU,CAAC;YAChB,KAAK,WAAW,CAAC;YACjB,KAAK,SAAS,CAAC;YACf,KAAK,UAAU,CAAC;YAChB,KAAK,OAAO;gBACV,OAAO,cAAc,CAAC,iBAAiB,CAAC;YAC1C,KAAK,SAAS;gBACZ,OAAO,cAAc,CAAC,iBAAiB,CAAC;YAC1C,KAAK,QAAQ;gBACX,OAAO,cAAc,CAAC,gBAAgB,CAAC;YACzC;gBACE,OAAO,cAAc,CAAC,iBAAiB,CAAC;SAC3C;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EACrE,UAAU,EAAE,MAAM,EAAE,qBAAqB,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC;QAClE,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,QAAQ,GAAG,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;SAC9C;QACD,IAAG,IAAI,IAAI,IAAI,EAAE;YACf,MAAM,IAAI,KAAK,CAAC,cAAc,CAAC,CAAC;SACjC;QACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,IAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;SAC1B;aAAM;YACL,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;YAC1D,IAAI,CAAC,QAAQ,CAAC,GAAG,GAAG,OAAO,GAAG,IAAI,CAAC,SAAS,EAAE,EAAE,CAAC;SAClD;IACH,CAAC;IAED,KAAK,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,OAAO;SACR;QACD,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YAC3B,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;YAC5B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;SAC/B;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,8BAA8B,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvB,CAAC;IAED,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;QAChC,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;QACjC,MAAM,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;IAC9B,CAAC;IAED,MAAM,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI;QAChC,IAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,oBAAoB,GAAG,YAAY,CAAA;YACxC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,YAAY,CAAC,CAAC;YAClC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;SACnB;aAAM;YACL,IAAI,CAAC,OAAO,GAAG,YAAY,CAAC;SAC7B;IACH,CAAC;IAED,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;QAC7B,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvC;IACH,CAAC;IAED,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI;QAC3B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;QAC1C,IAAG,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,SAAS,EAAE;YACrC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SACxC;IACH,CAAC;IAED,IAAI,QAAQ,IAAI,MAAM;QACpB,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;SAC/B;aAAM;YACL,OAAO,CAAC,CAAC;SACV;IACH,CAAC;IAED,IAAI,QAAQ,IAAI,MAAM;QACpB,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,EAAE;gBACrC,OAAO,IAAI,CAAC,oBAAoB,CAAA;aACjC;iBAAM;gBACL,OAAO,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;aAClC;SACF;aAAM;YACL,OAAO,CAAC,CAAC;SACV;IACH,CAAC;IAED,IAAI,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM;QAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,wBAAwB,CAAC,CAAC;QACrC,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;IAED,SAAS,IAAI,OAAO;QAClB,OAAO,IAAI,CAAC,QAAQ,EAAE,KAAK,KAAK,SAAS,CAAC;IAC5C,CAAC;IAED,OAAO,CAAC,mBAAmB,IAAI,IAAI;QACjC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC,iBAAiB,EAAE,EAAE;YACxF,QAAQ,KAAK,EAAE;gBACb,KAAK,MAAM;oBACT,MAAM;gBACR,KAAK,aAAa;oBAChB,IAAI,CAAC,QAAQ,EAAE,OAAO,EAAE,CAAC;oBACzB,MAAM;gBACR,KAAK,UAAU;oBACb,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM,IAAI,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC;oBAC5B,MAAM;gBACR,KAAK,WAAW;oBACd,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,QAAQ,EAAE,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;oBACnF,IAAI,CAAC,QAAQ,EAAE,2BAA2B,CAAC,IAAI,CAAC,CAAC;oBACjD,MAAM;gBACR,KAAK,SAAS;oBACZ,MAAM;gBACR,KAAK,UAAU;oBACb,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;oBAC9C,MAAM;gBACR,KAAK,SAAS;oBACZ,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,IAAI,CAAC,QAAQ,EAAE,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;oBACnE,MAAM;gBACR,KAAK,QAAQ;oBACX,IAAI,CAAC,iBAAiB,EAAE,CAAC;oBACzB,MAAM;aACT;QACH,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,OAAO,EAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YAC/C,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,kCAAkC,GAAG,GAAG,CAAC,IAAI,GAAE,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,CAAC;YACpF,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;QACzB,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,EAAC,CAAC,CAAC,EAAE,MAAM,EAAE,EAAE;YACzC,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAA;QAClC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,OAAO,CAAC,YAAY,CAAC,KAAK,EAAE,MAAM,GAAG,KAAK,CAAC,aAAa;QACtD,IAAI,UAAU,EAAG,iBAAiB,GAAG,iBAAiB,CAAC,WAAW,CAAC;QACnE,MAAM,UAAU,GAAG,MAAM,CAAC,MAAM,CAAC,iBAAiB,CAAC,IAAI,KAAK,CAAC,iBAAiB,CAAC,CAAC;QAChF,KAAI,MAAM,KAAK,IAAI,UAAU,EAAE;YAC7B,IAAG,KAAK,IAAI,KAAK,EAAE;gBACjB,UAAU,GAAG,KAAK,CAAC;gBACnB,MAAM;aACP;SACF;QACD,QAAQ,UAAU,EAAE;YAClB,KAAK,iBAAiB,CAAC,aAAa;gBAClC,OAAO,KAAK,CAAC,aAAa,CAAC,qBAAqB,CAAC;YACnD,KAAK,iBAAiB,CAAC,YAAY;gBACjC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,WAAW;gBAChC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,YAAY;gBACjC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,WAAW;gBAChC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,YAAY;gBACjC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,WAAW;gBAChC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,YAAY;gBACjC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD,KAAK,iBAAiB,CAAC,WAAW;gBAChC,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;YAClD;gBACE,OAAO,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC;SACnD;IACH,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,iBAAiB,IAAI,OAAO,CAAC,IAAI,CAAC;QAC9C,IAAG,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE;YACnB,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SACvC;QACD,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;QACxC,IAAG,IAAI,CAAC,OAAO,IAAI,CAAC,EAAE;YACpB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC,CAAC;SACnB;IACH,CAAC;CACF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/player/FlutterPlayer.ts": {"version": 3, "file": "FlutterPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/player/FlutterPlayer.ets"], "names": [], "mappings": "OAeO,EAAE;OACA,IAAI;OACN,EAAO,GAAG,EAAE;cAAV,GAAG;cACH,0BAA0B,QAAQ,sCAAsC;OAC1E,EAAE,OAAO,EAAE,cAAc,EAAE;cACzB,OAAO,QAAQ,WAAW;OAC5B,EAAE,eAAe,EAAE;OACnB,EAAE,oBAAoB,EAAE;AAE/B,MAAM,GAAG,GAAI,eAAe,CAAA;AAE5B,MAAM,OAAO,aAAa;IACxB,OAAO,CAAC,MAAM,EAAE,OAAO,GAAG,IAAI,GAAG,IAAI,CAAC;IACtC,OAAO,CAAC,YAAY,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAClC,OAAO,CAAC,WAAW,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IACjC,OAAO,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAChC,OAAO,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC;IAC/B,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;IACxB,MAAM,CAAC,eAAe,EAAE,MAAM,GAAG,CAAC,CAAC;IACnC,MAAM,CAAC,WAAW,EAAE,MAAM,GAAG,aAAa,CAAC;IAC3C,MAAM,CAAC,UAAU,EAAE,0BAA0B,GAAG,IAAI,GAAG,IAAI,CAAC;IAC5D,OAAO,CAAC,kBAAkB,GAAG,CAAC,CAAC;IAE/B,YAAY,QAAQ,EAAE,0BAA0B;QAC9C,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,UAAU,IAAI,OAAO;QAC1B,EAAE,aAAa,CAAC,eAAe,CAAC;QAChC,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,CAAC;QACtB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;QAC3C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC;QACvC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED,MAAM,CAAC,cAAc,IAAI,cAAc;QACrC,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACtB,OAAO,cAAc,CAAC,iBAAiB,CAAC;SACzC;aAAM;YACL,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;SAC1B;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;QAC3G,6BAA6B;QAC7B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,gCAAgC,CAAC,CAAC;QAC7C,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAC/E,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;QAC9E,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,UAAU,IAAI,IAAI,EAAE;YACtB,IAAI;gBACF,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;gBACtC,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,IAAI,MAAM,GAAG,EAAE,CAAC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;gBACjD,MAAM,CAAC,SAAS,CAAC,CAAC,UAAU,IAAI,UAAU,CAAC,CAAC,MAAM,CAAC,CAAC;gBACpD,MAAM,CAAC,SAAS,EAAE,CAAC;gBACnB,OAAO,GAAG,QAAQ,CAAC;aACpB;YAAC,OAAO,GAAG,EAAE;gBACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;gBAC3D,OAAO,KAAK,CAAC;aACd;SACF;QAED,IAAI;YACF,IAAI,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YACjC,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,EAAE;gBACxB,IAAI,CAAC,MAAM,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aAC5D;iBAAM;gBACH,IAAI,CAAC,MAAM,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;aACvD;YAED,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,IAAI,CAAC,cAAc,EAAE,CAAC;SAC7B;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,2BAA2B,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAC9D,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;QACtC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,EAAE,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;QAC1C,IAAI;YACF,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;YAC/B,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;gBACtB,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,KAAK,CAAC,CAAC;gBAC9C,OAAO,KAAK,CAAC;aACd;YACD,MAAM,IAAI,CAAC,MAAM,EAAE,WAAW,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,EAAE,oBAAoB,CAAC,IAAI,CAAC,CAAC;YAC5C,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,yBAAyB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI;YACF,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;gBACtB,OAAO,KAAK,CAAC;aACd;YACD,MAAM,IAAI,CAAC,MAAM,EAAE,YAAY,EAAE,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;YACvC,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;YAC7C,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,CAAC,EAAE;YACV,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;YAC3D,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;QAC1C,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAC;YACzB,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC;QACrB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,MAAM,CAAC,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,OAAO;QACvC,IAAI;YACF,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC;YAC3B,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;gBACtB,OAAO,KAAK,CAAC;aACd;YACD,IAAI,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YAC/B,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,uBAAuB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1D,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,OAAO;QACrC,IAAI;YACF,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;gBACtB,OAAO,KAAK,CAAC;aACd;YACD,IAAI,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;SACb;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sBAAsB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YACzD,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;QACpD,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACtB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACxC;IACH,CAAC;IAED,MAAM,CAAC,WAAW,IAAI,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC;QACpC,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAI,QAAQ,GAAG,CAAC,CAAC;QACjB,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACtB,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;YAChC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC;SACjC;QACD,IAAG,QAAQ,GAAG,QAAQ,EAAE;YACtB,QAAQ,GAAG,QAAQ,CAAC;SACrB;QACD,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACtC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9B,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9B,GAAG,CAAC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAC/C,OAAO,GAAG,CAAC;IACb,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM;QACpC,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACtB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;SAC1C;QAED,IAAI;YACF,IAAI,EAAE,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAChC,IAAG,EAAE,GAAG,CAAC,EAAE;gBACT,MAAM,IAAI,KAAK,CAAC,mBAAmB,CAAC,CAAC;aACtC;YACD,OAAO,EAAE,CAAC;SACX;QAAC,OAAO,GAAG,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACtC;IACH,CAAC;IAED,kBAAkB,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO;QACzC,QAAQ,KAAK,EAAE;YACb,KAAK,OAAO,CAAC,OAAO,CAAC;YACrB,KAAK,OAAO,CAAC,MAAM,CAAC;YACpB,KAAK,OAAO,CAAC,GAAG,CAAC;YACjB,KAAK,OAAO,CAAC,SAAS,CAAC;YACvB,KAAK,OAAO,CAAC,OAAO,CAAC;YACrB,KAAK,OAAO,CAAC,KAAK,CAAC;YACnB,KAAK,OAAO,CAAC,QAAQ,CAAC;YACtB,KAAK,OAAO,CAAC,IAAI;gBACf,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,KAAK,CAAC,CAAC,4BAA4B;SAC7C;IACH,CAAC;IAID,OAAO,CAAC,KAAK,CAAC,IAAI;QAChB,IAAI,CAAC,cAAc,EAAE,CAAC;QACtB,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAG,IAAI,CAAC,MAAM,EAAE;YACd,MAAM,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,CAAC;SAChC;QACD,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAED,OAAO,CAAC,eAAe,IAAI,MAAM;QAC/B,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAG,iBAAiB,GAAG,IAAI,CAAC,UAAU,CAAC;IACzE,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;QAC3C,IAAI,QAAQ,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACtC,IAAI;YACF,IAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;gBAC1B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACxB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,eAAe,GAAG,QAAQ,CAAC,CAAC;aACxC;SACF;QAAC,OAAO,GAAG,EAAE;SACb;IACH,CAAC;IAED,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;QAC3C,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAI,GAAG,GAAG,OAAO,CAAC;IACpD,CAAC;IAED,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;QACnC,IAAG,IAAI,IAAI,IAAI,EAAE;YACf,OAAO,EAAE,CAAC;SACX;aAAM;YACL,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAG,CAAC,OAAO,EAAE;gBACX,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;aAChC;YACD,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;QACtC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QAC/B,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,IAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;YAC9B,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,GAAG,EAAE;gBACjC,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,EAAE;oBACjD,IAAI,CAAC,UAAU,EAAE,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;iBAC7E;YACH,CAAC,EAAE,QAAQ,CAAC,CAAC;SACd;IACH,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,cAAc,IAAI,OAAO,CAAC,OAAO,CAAC;QAC9C,IAAG,IAAI,CAAC,MAAM,IAAI,IAAI,EAAE;YACtB,OAAO,KAAK,CAAC;SACd;QACD,IAAI;YACF,IAAI,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE;gBAC1B,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;aACnC;YACD,IAAG,IAAI,CAAC,WAAW,IAAI,CAAC,EAAE;gBACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;aACjC;YACD,IAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACxC;YACD,IAAG,IAAI,CAAC,UAAU,IAAI,CAAC,EAAE;gBACvB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aACpC;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YACvD,OAAO,KAAK,CAAC;SACd;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/player/IPlayer.ts": {"version": 3, "file": "IPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/player/IPlayer.ets"], "names": [], "mappings": "cAckB,cAAc,QAAQ,6BAA6B;AAErE,MAAM,WAAW,OAAO;IACtB,WAAW,CAAC,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAC/D,UAAU,EAAE,MAAM,EAAE,qBAAqB,EAAE,OAAO,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC;IACrE,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC5B,WAAW,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC7B,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC,CAAC;IAC9B,MAAM,CAAC,YAAY,EAAE,MAAM,GAAG,IAAI,CAAC;IACnC,SAAS,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC;IAChC,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IAC9B,IAAI,QAAQ,IAAI,MAAM,CAAC;IACvB,IAAI,QAAQ,IAAI,MAAM,CAAC;IACvB,IAAI,KAAK,IAAI,cAAc,CAAC;IAC5B,IAAI,CAAC,IAAI,EAAE,WAAW,GAAG,MAAM,CAAC;IAChC,SAAS,IAAI,OAAO,CAAC;CACtB", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundManager.ts": {"version": 3, "file": "FlutterSoundManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundManager.ets"], "names": [], "mappings": "cAeS,UAAU,EAAE,aAAa;cACzB,YAAY;cACZ,mBAAmB,QAAQ,uBAAuB;AAE3D,MAAM,OAAO,mBAAmB;IAC9B,MAAM,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IAC5C,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,mBAAmB,GAAG,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;IAEvE,IAAI,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAI;QACjC,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;IAC1B,CAAC;IAED,YAAY,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI;QAChE,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IAC9C,CAAC;IAED,QAAQ,CAAC,MAAM,EAAE,MAAM,GAAG,IAAI;QAC5B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IACpC,CAAC;IAED,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,GAAG,mBAAmB,GAAG,SAAS;QAClE,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC7C,IAAG,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YACzC,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;SACxC;QACD,IAAI,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;SACnC;QACD,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAChC,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,mBAAmB,GAAG,IAAI;QACtE,IAAI,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC3C,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;QAC9B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACrB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY;QACvD,KAAI,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE;YACvC,IAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,SAAS,EAAE;gBACjC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;aACxC;YACD,IAAI,CAAC,KAAK,GAAG,IAAI,GAAG,EAAE,CAAC;SACxB;QACD,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;CACF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundPlayer.ts": {"version": 3, "file": "FlutterSoundPlayer.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundPlayer.ets"], "names": [], "mappings": "cAeS,GAAG,EAAO,UAAU;cACpB,YAAY;cACZ,mBAAmB,QAAQ,uBAAuB;OACpD,EAAE,yBAAyB,EAAE;OAC7B,EAAE,mBAAmB,EAAE;OACvB,EAAW,WAAW,EAAqC;cAAzD,OAAO,EAAe,cAAc;YAEpC,MAAM;YACR,EAAE;cAEA,0BAA0B,QAAQ,8BAA8B;OAClE,EAAE,aAAa,EAAE;AAExB,MAAM,GAAG,GAAG,oBAAoB,CAAC;AACjC,MAAM,OAAO,kBAAmB,SAAQ,mBAAoB,YAAW,0BAA0B;IAC/F,MAAM,CAAC,WAAW,GAAa,aAAa,CAAC;IAC7C,MAAM,CAAC,kBAAkB,GAAM,oBAAoB,CAAC;IACpD,MAAM,CAAC,qBAAqB,GAAG,uBAAuB,CAAC;IACvD,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpD,OAAO,CAAC,OAAO,EAAE,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IAG7C,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC;IAEzC,sHAAsH;IACtH,mBAAmB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QACzC,IAAI,CAAC,uBAAuB,CAAE,qBAAqB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC1E,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QAC1C,IAAI,CAAC,uBAAuB,CAAE,sBAAsB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC3E,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QACzC,IAAI,CAAC,uBAAuB,CAAE,qBAAqB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC1E,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QAC1C,IAAI,CAAC,uBAAuB,CAAE,sBAAsB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC3E,CAAC;IAED,qBAAqB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QAC3C,IAAI,CAAC,uBAAuB,CAAE,uBAAuB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC5E,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;QAC5D,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,sBAAsB,EAAE,OAAO,EAAE,GAAG,CAAC,CAAC;IACjE,CAAC;IAED,YAAY,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI;QAC5B,IAAI,CAAC,uBAAuB,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE,CAAC,CAAC;IACzD,CAAC;IAED,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI;QACtD,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9B,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9B,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;QAC9C,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACxD,CAAC;IAED,2BAA2B,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI;QAC9C,IAAI,CAAC,uBAAuB,CAAC,4BAA4B,EAAE,IAAI,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC1F,CAAC;IAED,mBAAmB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI;QACjD,IAAI,CAAC,uBAAuB,CAAC,qBAAqB,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC;IACtE,CAAC;IAED,sHAAsH;IAEtH,YAAY,IAAI,EAAE,UAAU;QAC1B,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,OAAO,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;IACzC,CAAC;IAED,SAAS,IAAI,mBAAmB;QAC9B,OAAO,yBAAyB,CAAC,wBAAwB,CAAC;IAC5D,CAAC;IAED,SAAS,IAAI,MAAM;QACjB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC;IAC/B,CAAC;IAED,cAAc,IAAI,cAAc;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC,cAAc,EAAE,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QACrE,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;QACrC,IAAG,GAAG,EAAE;YACN,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;SACvC;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;SACzG;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QACtE,MAAM,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAChE,MAAM,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC;QAClC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,kBAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC9D,IAAI,UAAU,GAAG,IAAI,CAAC;QACtB,IAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;YACrC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SACzC;QACD,IAAI,WAAW,GAAG,KAAK,CAAC;QACxB,IAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE;YACtC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SAC3C;QACD,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE;YACvC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;SAC7C;QACD,IAAI;YACF,IAAI,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;YAClF,IAAG,GAAG,EAAE;gBACN,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACvC;iBAAM;gBACL,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,4BAA4B,CAAC,CAAC;aAC5G;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,gCAAgC,CAAC,CAAC;YAC9D,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACnG;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC7E,IAAI,MAAM,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;QAC5C,IAAI,KAAK,EAAE,OAAO,GAAG,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;QACnD,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAC,IAAI,WAAW,CAAC;QAChE,IAAI,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC;QAC9B,IAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,IAAI,EAAE;YACrC,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;SACzC;QACD,IAAI,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC7C,IAAI,WAAW,EAAE,MAAM,GAAG,KAAK,CAAC;QAChC,IAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE;YACtC,WAAW,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;SAC3C;QACD,IAAI,YAAY,EAAE,MAAM,GAAG,CAAC,CAAC;QAC7B,IAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,IAAI,EAAE;YACvC,YAAY,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;SAC7C;QACD,IAAI;YACF,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,YAAY,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;YAC3G,IAAG,GAAG,EAAE;gBACN,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACvC;iBAAM;gBACL,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;aACrG;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACnG;IACL,CAAC;IAEC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACvD,IAAI;YACF,IAAI,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC;YAChD,IAAI,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;SACpB;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,kBAAkB,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACnG;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC5E,MAAM,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;QACjC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACrE,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAG,OAAO,CAAE,IAAI,MAAM,CAAC;QAChD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC1D,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC7E,IAAI;YACF,IAAG,MAAM,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,EAAE;gBACpC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;aACvC;iBAAM;gBACL,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,qBAAqB,CAAC,CAAC;aACrG;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,yBAAyB,CAAC,CAAC;YACvD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACnG;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9E,IAAI;YACF,IAAI,MAAM,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE;gBACpC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;;gBAEtC,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,sBAAsB,CAAC,CAAC;SACxG;QAAC,OAAO,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,KAAK,EAAE,0BAA0B,CAAC,CAAC;YACxD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SACnG;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9E,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAG,UAAU,CAAE,IAAI,MAAM,CAAC;QACpD,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,MAAM,CAAC,CAAC;QACnC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC5D,IAAI;YACF,IAAI,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAG,QAAQ,CAAE,IAAI,MAAM,CAAC;YAClD,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;YAChC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;SACvC;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACjG;IACH,CAAC;IAED,MAAM,CAAC,QAAQ,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC3D,IAAI;YACF,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAG,OAAO,CAAE,IAAI,MAAM,CAAC;YAChD,IAAI,CAAC,OAAO,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;YAC9B,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;SACvC;QAAC,OAAO,CAAC,EAAE;YACV,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAAC,WAAW,EAAE,kBAAkB,CAAC,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;SACjG;IAEH,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC1E,IAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;YACpC,IAAI,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,MAAM,CAAC;YACnD,IAAI,CAAC,OAAO,EAAE,uBAAuB,CAAC,QAAQ,CAAC,CAAC;SACjD;QACD,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC9D,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,EAAE;YACxB,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE,CAAC;YACxD,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC/B,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;SACrB;IACH,CAAC;IAED,MAAM,CAAC,eAAe,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAClE,OAAO;QACP,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;IACrB,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC1E,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;IAChE,CAAC;CAEF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundPlayerCallback.ts": {"version": 3, "file": "FlutterSoundPlayerCallback.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundPlayerCallback.ets"], "names": [], "mappings": "cAcS,cAAc,QAAQ,qBAAqB;AAEpD,MAAM,WAAW,0BAA0B;IACzC,mBAAmB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC5C,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC7C,mBAAmB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC5C,oBAAoB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC7C,qBAAqB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC9C,oBAAoB,CAAC,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/D,YAAY,CAAC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,cAAc,CAAC,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IACzD,2BAA2B,CAAC,IAAI,EAAE,OAAO,GAAG,IAAI,CAAC;IACjD,mBAAmB,CAAC,QAAQ,EAAE,cAAc,GAAG,IAAI,CAAC;CACrD", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundPlayerManager.ts": {"version": 3, "file": "FlutterSoundPlayerManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundPlayerManager.ets"], "names": [], "mappings": "OAeO,EAAmB,GAAG,EAAc;cAAlC,eAAe,EAAO,UAAU;OAClC,aAAkD;cAAjC,iBAAiB,EAAE,YAAY;OAChD,EAAE,mBAAmB,EAAE;YACrB,MAAM;OACR,EAAE,kBAAkB,EAAE;cACpB,SAAS,IAAT,SAAS;OAAE,SAAS;OACpB,qBAAqB;cACrB,aAAa,IAAb,aAAa;OACA,gBAAgB;AAEtC,MAAM,GAAG,GAAG,2BAA2B,CAAC;AAExC,MAAM,OAAO,yBAA0B,SAAQ,mBAC7C,YAAW,iBAAiB;IAC5B,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpD,MAAM,CAAC,wBAAwB,EAAE,yBAAyB,CAAC,CAAC,WAAW;IACvE,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACtC,OAAO,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,SAAS,GAAG,SAAS,CAAC;IAEzD,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,GAAG,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,EAAE,QAAQ,EAAE,eAAe,GAAG,SAAS,GAAG,IAAI;QACrH,IAAG,yBAAyB,CAAC,wBAAwB,IAAI,IAAI,EAAE;YAC7D,yBAAyB,CAAC,wBAAwB,GAAG,IAAI,yBAAyB,EAAE,CAAC;SACtF;QACD,IAAG,QAAQ,IAAI,SAAS,EAAE;YACxB,IAAI,OAAO,EAAE,aAAa,GAAG,IAAI,aAAa,CAAC,QAAQ,EAAE,oCAAoC,CAAC,CAAC;YAC/F,yBAAyB,CAAC,wBAAwB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACjE,OAAO,CAAC,oBAAoB,CAAC,yBAAyB,CAAC,wBAAwB,CAAC,CAAC;SAClF;QACD,yBAAyB,CAAC,OAAO,GAAG,GAAG,CAAC;IAC1C,CAAC;IAED,UAAU,IAAI,yBAAyB;QACrC,OAAO,yBAAyB,CAAC,wBAAwB,CAAC;IAC5D,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,aAAa,CAAC,CAAC;gBAClB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC/B,OAAO;aACR;SACF;QACD,IAAI,OAAO,EAAE,kBAAkB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC;QAC9E,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,YAAY,CAAC,CAAC;gBACjB,OAAO,GAAG,IAAI,kBAAkB,CAAC,IAAI,CAAC,CAAC;gBACvC,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBAChC,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACjC,MAAM;aACP;YACD,KAAK,aAAa,CAAC,CAAC;gBAClB,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAClC,MAAM;aACP;YACD,KAAK,oBAAoB,CAAC,CAAC;gBACzB,OAAO,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACzC,MAAM;aACP;YACD,KAAK,gBAAgB,CAAC,CAAC;gBACrB,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC9C,MAAM;aACP;YACD,KAAK,iBAAiB,CAAC,CAAC;gBACtB,OAAO,CAAC,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACtC,MAAM;aACP;YACD,KAAK,aAAa,CAAC,CAAC;gBAClB,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAClC,MAAM;aACP;YACD,KAAK,aAAa,CAAC,CAAC;gBAClB,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAClC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM;aACP;YACD,KAAK,oBAAoB,CAAC,CAAC;gBACzB,4CAA4C;gBAC5C,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;aACP;YACD,KAAK,YAAY,CAAC,CAAC;gBACjB,OAAO,CAAC,UAAU,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACjC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;aACP;YACD,KAAK,aAAa,CAAC,CAAC;gBAClB,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAClC,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC1B,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACnC,IAAI,CAAC,mBAAmB,EAAE,CAAC;gBAC3B,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,OAAO,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACnC,MAAM;aACP;YACD,KAAK,WAAW,CAAC,CAAC;gBAChB,OAAO,CAAC,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAChC,MAAM;aACP;YACD,KAAK,UAAU,CAAC,CAAC;gBACf,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC/B,MAAM;aACP;YACD,KAAK,yBAAyB,CAAC,CAAC;gBAC9B,OAAO,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC9C,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;aACP;YACD,KAAK,MAAM,CAAC,CAAC;gBACX,8BAA8B;gBAC9B,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;aACP;YACD,KAAK,aAAa,CAAC,CAAC;gBAClB,OAAO,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAClC,MAAM;aACP;YACD,OAAO,CAAC,CAAC;gBACP,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;aACP;SACF;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClC,IAAG,IAAI,CAAC,qBAAqB,EAAC;YAC5B,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,IAAI,OAAO,GAAG,yBAAyB,CAAC,OAAO,CAAC;QAChD,IAAI,IAAI,EAAE,gBAAgB,CAAC,aAAa,GAAG,OAAO,CAAC;QACnD,MAAM,gBAAgB,CAAC,eAAe,CAAC,OAAO,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YAClF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAC1B,CAAC,CAAC,CAAC;QACH,0BAA0B;QAC1B,MAAM,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC;QAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qCAAqC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAE3E,IAAI,aAAa,EAAE,SAAS,CAAC,aAAa,GAAG;YAC3C,KAAK,EAAE;gBACL;oBACE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,UAAU;oBAC3C,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC,IAAI;iBACvC;aACF;YACD,UAAU,EAAE,SAAS,CAAC,aAAa,CAAC,aAAa;YACjD,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC;SAC/D,CAAC;QAEF,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;YACrE,qBAAqB,CAAC,sBAAsB,CAAC,OAAO,EAAE,qBAAqB,CAAC,cAAc,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBACjI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mDAAmD,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;gBAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,uDAAuD,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QACjC,IAAG,CAAC,IAAI,CAAC,qBAAqB,EAAC;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,MAAM,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;QAC9B,qBAAqB,CAAC,qBAAqB,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACvF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,kDAAkD,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sDAAsD,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundPlugin.ts": {"version": 3, "file": "FlutterSoundPlugin.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundPlugin.ets"], "names": [], "mappings": "cAeS,aAAa,EAAE,oBAAoB;cACnC,YAAY,EAAE,oBAAoB;YAClC,MAAM;OACR,EAAE,yBAAyB,EAAE;OAC7B,EAAE,2BAA2B,EAAE;OAC/B,EAAE,kBAAkB,EAAE;OACtB,EAAE,oBAAoB,EAAE;AAE/B,0BAA0B;AAC1B,MAAM,CAAC,OAAO,OAAO,kBAAmB,YAAW,aAAa,EAAE,YAAY;IAC5E,OAAO,CAAC,aAAa,EAAE,oBAAoB,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1D,OAAO,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,OAAO,GAAG,SAAS,GAAG,SAAS,CAAC;IAE/D;IACA,CAAC;IAED,mBAAmB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACtD,kFAAkF;QAClF,kBAAkB,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,EAAE,qBAAqB,EAAE,CAAC;QACzE,kBAAkB,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC;QAC1D,oBAAoB,CAAC,OAAO,GAAG,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,CAAC;QAC5D,yBAAyB,CAAC,kBAAkB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,kBAAkB,EAAE,CAAC,CAAC;QACrH,2BAA2B,CAAC,oBAAoB,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAC3H,CAAC;IAED,qBAAqB,IAAI,IAAI;IAC7B,CAAC;IAED,kBAAkB,IAAI,MAAM;QAC1B,OAAO,oBAAoB,CAAA;IAC7B,CAAC;IAED,kBAAkB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;QACrD,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC;IAC/B,CAAC;IAED,oBAAoB,CAAC,OAAO,EAAE,oBAAoB,GAAG,IAAI;IAEzD,CAAC;CAIF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundRecorder.ts": {"version": 3, "file": "FlutterSoundRecorder.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundRecorder.ets"], "names": [], "mappings": "cAeS,GAAG,EAAE,UAAU;cACf,YAAY;cACZ,mBAAmB,QAAQ,uBAAuB;OACpD,EAAE,2BAA2B,EAAE;OAC/B,EAAE,mBAAmB,EAAE;OACvB,EAA2B,gBAAgB,EAAE;cAA3C,cAAc,EAAE,OAAO;YACvB,MAAM;cACN,4BAA4B,QAAQ,gCAAgC;OACtE,EAAE,eAAe,EAAE;AAE1B,MAAM,GAAG,GAAG,sBAAsB,CAAC;AACnC,MAAM,OAAO,oBAAqB,SAAQ,mBAAoB,YAAW,4BAA4B;IACnG,MAAM,CAAC,WAAW,GAAkB,aAAa,CAAC;IAClD,MAAM,CAAC,oBAAoB,GAAS,sBAAsB,CAAC;IAC3D,MAAM,CAAC,yBAAyB,GAAI,2BAA2B,CAAC;IAChE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpD,OAAO,CAAC,SAAS,EAAE,eAAe,GAAG,IAAI,GAAG,IAAI,CAAC;IAEjD,sHAAsH;IACtH,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QAClD,IAAI,CAAC,uBAAuB,CAAE,uBAAuB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC5E,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QACnD,IAAI,CAAC,uBAAuB,CAAE,wBAAwB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC7E,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI;QAC/D,IAAI,CAAC,sBAAsB,CAAE,uBAAuB,EAAE,OAAO,EAAE,GAAG,CAAE,CAAC;IACvE,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QACnD,IAAI,CAAC,uBAAuB,CAAE,wBAAwB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC7E,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QACpD,IAAI,CAAC,uBAAuB,CAAE,yBAAyB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC9E,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI;QACnD,IAAI,CAAC,uBAAuB,CAAE,wBAAwB,EAAE,OAAO,EAAE,OAAO,CAAE,CAAC;IAC7E,CAAC;IAED,MAAM,CAAC,iCAAiC,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;QAC5E,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACtC,GAAG,CAAC,GAAG,CAAC,aAAa,EAAE,WAAW,CAAC,CAAC;QACpC,GAAG,CAAC,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC;QAC9B,IAAI,CAAC,mBAAmB,CAAC,wBAAwB,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IAChE,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW;QACpC,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACtC,GAAG,CAAC,GAAG,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;QAC/B,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;IACvD,CAAC;IACD,sHAAsH;IAEtH,YAAY,IAAI,EAAE,UAAU;QAC1B,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,SAAS,GAAG,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC;IAC7C,CAAC;IAED,SAAS,IAAI,mBAAmB;QAC9B,OAAO,2BAA2B,CAAC,0BAA0B,CAAC;IAChE,CAAC;IAED,SAAS,IAAI,MAAM;QACjB,IAAG,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;YACzB,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;SAC7C;aAAM;YACL,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC;SAC1C;IACH,CAAC;IAED,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAChE,MAAM,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QACtC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;IACpB,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QACvE,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC;QAC/C,IAAG,GAAG,EAAE;YACN,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC;SAChC;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,oBAAoB,CAAC,WAAW,EAAE,oBAAoB,CAAC,WAAW,EAAE,yBAAyB,CAAC,CAAC;SAC7G;IACH,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QACxE,MAAM,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QACtC,MAAM,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IAClC,CAAC;IAED,kBAAkB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC9D,IAAI,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAG,OAAO,CAAE,IAAI,OAAO,CAAC;QACjD,IAAI,WAAW,EAAE,OAAO,GAAG,KAAK,CAAC;QACjC,IAAG,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;YACzB,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC;SACxD;QACD,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;IAC9B,CAAC;IAED,kBAAkB,CAAC,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI;QACvD,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,IAAI,GAAG,EAAE,CAAC;QACtC,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QACxE,IAAI,UAAU,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QACrD,IAAI,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QACvD,IAAI,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;QAC/C,IAAI,KAAK,EAAE,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,OAAO,CAAC;QACvD,IAAI,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,WAAW,EAAE,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC;QAC/D,IAAI,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACjD,IAAI,GAAG,EAAE,OAAO,GAAG,KAAK,CAAC;QACzB,IAAG,IAAI,CAAC,SAAS,IAAI,IAAI,EAAE;YACzB,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,CAAC,aAAa,CAAC,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAC/E,IAAI,EAAE,WAAW,EAAE,QAAQ,IAAE,CAAC,CAAC,CAAC;SACnC;QACD,IAAG,GAAG,EAAE;YACN,MAAM,CAAC,OAAO,CAAC,2BAA2B,CAAC,CAAC;SAC7C;aAAM;YACL,MAAM,CAAC,KAAK,CAAC,eAAe,EAAE,eAAe,EAAE,2BAA2B,CAAC,CAAC;SAC7E;IACH,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9E,MAAM,IAAI,CAAC,SAAS,EAAE,YAAY,EAAE,CAAC;QACrC,MAAM,CAAC,OAAO,CAAC,0BAA0B,CAAC,CAAC;IAC7C,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC/E,MAAM,IAAI,CAAC,SAAS,EAAE,aAAa,EAAE,CAAC;QACtC,MAAM,CAAC,OAAO,CAAE,oBAAoB,CAAC,CAAC;IACxC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAChF,MAAM,IAAI,CAAC,SAAS,EAAE,cAAc,EAAE,CAAC;QACvC,MAAM,CAAC,OAAO,CAAE,qBAAqB,CAAC,CAAC;IACzC,CAAC;IAED,MAAM,CAAC,uBAAuB,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC1E,IAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,IAAI,EAAE;YACpC,OAAM;SACP;QACD,IAAI,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QACjD,IAAI,CAAC,SAAS,EAAE,qBAAqB,CAAC,QAAQ,CAAC,CAAC;QAChD,MAAM,CAAC,OAAO,CAAG,2BAA2B,GAAG,QAAQ,CAAE,CAAC;IAC5D,CAAC;IAGD,MAAM,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QAC/D,IAAI,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,GAAG,GAAG,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QAC7C,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,OAAO,CAAC,IAAI,CAAC;QAC9E,IAAI,IAAI,EAAE,MAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,GAAG,GAAG,MAAM,IAAI,CAAC,SAAS,EAAE,YAAY,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;IACtB,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;IAChE,CAAC;CAEF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundRecorderCallback.ts": {"version": 3, "file": "FlutterSoundRecorderCallback.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundRecorderCallback.ets"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,MAAM,WAAW,4BAA4B;IAC3C,qBAAqB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC9C,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC/C,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC/C,qBAAqB,CAAC,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3D,sBAAsB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAC/C,uBAAuB,CAAC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IAChD,iCAAiC,CAAC,WAAW,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/E,aAAa,CAAC,IAAI,EAAE,WAAW,GAAG,IAAI,CAAC;CACxC", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundRecorderManager.ts": {"version": 3, "file": "FlutterSoundRecorderManager.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundRecorderManager.ets"], "names": [], "mappings": "OAeO,EAAmB,GAAG,EAAc;cAAlC,eAAe,EAAO,UAAU;OAClC,aAAkD;cAAjC,iBAAiB,EAAE,YAAY;OAChD,EAAE,mBAAmB,EAAE;YACrB,MAAM;OACR,EAAE,oBAAoB,EAAE;cACtB,SAAS,IAAT,SAAS;OAAE,SAAS;OACpB,qBAAqB;cACrB,aAAa,IAAb,aAAa;OACA,gBAAgB;AAEtC,MAAM,GAAG,GAAG,6BAA6B,CAAC;AAC1C,MAAM,OAAO,2BAA4B,SAAQ,mBAC/C,YAAW,iBAAiB;IAE5B,MAAM,CAAC,0BAA0B,EAAE,2BAA2B,CAAC,CAAC,WAAW;IAC3E,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,CAAC;IACpD,OAAO,CAAC,qBAAqB,GAAG,KAAK,CAAC;IACtC,OAAO,CAAC,OAAO,CAAC,EAAE,gBAAgB,CAAC,SAAS,GAAG,SAAS,CAAC;IAEzD,MAAM,CAAC,MAAM,CAAC,oBAAoB,CAAC,GAAG,EAAE,MAAM,CAAC,gBAAgB,GAAG,SAAS,EAAE,SAAS,EAAE,eAAe,GAAG,SAAS,GAAG,IAAI;QACxH,IAAG,2BAA2B,CAAC,0BAA0B,IAAI,IAAI,EAAE;YACjE,2BAA2B,CAAC,0BAA0B,GAAG,IAAI,2BAA2B,EAAE,CAAC;SAC5F;QACD,IAAG,SAAS,IAAI,SAAS,EAAE;YACzB,IAAI,OAAO,EAAE,aAAa,GAAG,IAAI,aAAa,CAAC,SAAS,EAAE,sCAAsC,CAAC,CAAC;YAClG,2BAA2B,CAAC,0BAA0B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACrE,OAAO,CAAC,oBAAoB,CAAC,2BAA2B,CAAC,0BAA0B,CAAC,CAAC;SACtF;QACD,2BAA2B,CAAC,OAAO,GAAG,GAAG,CAAC;IAC5C,CAAC;IAED,UAAU,IAAI,2BAA2B;QACvC,OAAO,2BAA2B,CAAC,0BAA0B,CAAC;IAChE,CAAC;IAED,YAAY,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI;QACxD,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,aAAa,CAAC,CAAC;gBAClB,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC/B,MAAM;aACP;SACF;QAED,IAAI,SAAS,EAAE,oBAAoB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,oBAAoB,CAAC;QAEpF,QAAQ,IAAI,CAAC,MAAM,EAAE;YACnB,KAAK,cAAc,CAAC,CAAC;gBACnB,SAAS,GAAG,IAAI,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;gBAClC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrC,MAAM;aACP;YACD,KAAK,eAAe,CAAC,CAAC;gBACpB,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACtC,MAAM;aACP;YACD,KAAK,oBAAoB,CAAC,CAAC;gBACzB,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAC3C,MAAM;aACP;YACD,KAAK,eAAe,CAAC,CAAC;gBACpB,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACtC,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrC,MAAM;aACP;YACD,KAAK,yBAAyB,CAAC,CAAC;gBAC9B,SAAS,CAAC,uBAAuB,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBAChD,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;aACP;YACD,KAAK,eAAe,CAAC,CAAC;gBACpB,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACtC,MAAM;aACP;YACD,KAAK,gBAAgB,CAAC,CAAC;gBACrB,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACvC,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrC,MAAM;aACP;YACD,KAAK,cAAc,CAAC,CAAC;gBACnB,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrC,MAAM;aACP;YACD,KAAK,aAAa,CAAC,CAAC;gBAClB,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACpC,MAAM;aACP;YACD,OAAQ,CAAC,CAAC;gBACR,MAAM,CAAC,cAAc,EAAE,CAAC;gBACxB,MAAM;aACP;SACF;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qBAAqB,CAAC,CAAC;QAClC,IAAG,IAAI,CAAC,qBAAqB,EAAC;YAC5B,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC;QAElC,IAAI,OAAO,GAAG,2BAA2B,CAAC,OAAO,CAAC;QAClD,IAAI,IAAI,EAAE,gBAAgB,CAAC,aAAa,GAAG,OAAO,CAAC;QACnD,MAAM,gBAAgB,CAAC,eAAe,CAAC,OAAO,EAAE,gBAAgB,EAAE,IAAI,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;YACrF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACzB,CAAC,CAAC,CAAC;QACH,0BAA0B;QAC1B,MAAM,IAAI,CAAC,OAAO,EAAE,QAAQ,EAAE,CAAC;QAC/B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,qCAAqC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,CAAC;QAE3E,IAAI,aAAa,EAAE,SAAS,CAAC,aAAa,GAAG;YAC3C,KAAK,EAAE;gBACL;oBACE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC,UAAU;oBAC3C,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC,IAAI;iBACvC;aACF;YACD,UAAU,EAAE,SAAS,CAAC,aAAa,CAAC,aAAa;YACjD,WAAW,EAAE,CAAC;YACd,cAAc,EAAE,CAAC,SAAS,CAAC,cAAc,CAAC,mBAAmB,CAAC;SAC/D,CAAC;QAEF,SAAS,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,YAAY,EAAE,SAAS,EAAE,EAAE;YACrE,qBAAqB,CAAC,sBAAsB,CAAC,OAAO,EAAE,qBAAqB,CAAC,cAAc,CAAC,eAAe,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;gBAClI,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,mDAAmD,CAAC,CAAC;YAClE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;gBAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,uDAAuD,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;YAC3G,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,kBAAkB;QACtB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;QACjC,IAAG,CAAC,IAAI,CAAC,qBAAqB,EAAC;YAC7B,OAAO;SACR;QACD,IAAI,CAAC,qBAAqB,GAAG,KAAK,CAAC;QACnC,MAAM,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC;QAC9B,qBAAqB,CAAC,qBAAqB,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;YACzF,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,kDAAkD,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YAC9B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,sDAAsD,GAAG,CAAC,IAAI,gBAAgB,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC;QAC1G,CAAC,CAAC,CAAC;IACL,CAAC;CACF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundSession.ts": {"version": 3, "file": "FlutterSoundSession.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundSession.ets"], "names": [], "mappings": "cAeS,UAAU;cACV,YAAY;cACZ,mBAAmB,QAAQ,uBAAuB;cAClD,WAAW,QAAQ,qBAAqB;AAEjD,MAAM,CAAC,QAAQ,OAAO,mBAAmB;IACvC,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,CAAC,CAAC;IAE1B,YAAY,IAAI,EAAE,UAAU;IAC5B,CAAC;IAED,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;QAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;IACrB,CAAC;IAED,QAAQ,CAAC,SAAS,IAAK,mBAAmB,CAAC;IAE3C,cAAc,IAAI,IAAI;QACpB,IAAI,CAAC,SAAS,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;IACzC,CAAC;IAED,QAAQ,CAAC,SAAS,IAAI,MAAM,CAAC;IAE7B,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,GAAG,IAAI,CAAC;IAE7D,sBAAsB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI;QAC7E,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI;QAC9E,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,uBAAuB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAG,IAAI;QAC/E,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,mBAAmB,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI;QACzF,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QAC5B,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;IACjD,CAAC;IAED,GAAG,CAAC,KAAK,EAAE,WAAW,EAAE,GAAG,EAAE,MAAM,GAAG,IAAI;QACxC,IAAI,GAAG,EAAE,GAAG,CAAC,MAAM,MAAW,GAAG,IAAI,GAAG,EAAE,CAAC;QAC3C,GAAG,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;QAC/B,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;QACnC,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAClC,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACpB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QACzB,IAAI,CAAC,SAAS,EAAE,CAAC,YAAY,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAC5C,CAAC;CAEF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/plugin/FlutterSoundTypes.ts": {"version": 3, "file": "FlutterSoundTypes.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/plugin/FlutterSoundTypes.ets"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;GAaG;AAEH,MAAM,MAAM,cAAc;IACxB,iBAAiB,IAAA;IACjB,iBAAiB,IAAA;IACjB,gBAAgB,IAAA;CACjB;AAED,MAAM,MAAM,WAAW;IACrB,OAAO,IAAA;IACP,GAAG,IAAA;IACH,IAAI,IAAA;IACJ,OAAO,IAAA;IACP,KAAK,IAAA;IACL,GAAG,IAAA;IACH,OAAO,IAAA;CACR;AAED,MAAM,MAAM,OAAO;IACjB,YAAY,IAAA;IACZ,OAAO,IAAA;IACP,OAAO,IAAA;IACP,OAAO,IAAA;IACP,GAAG,IAAA;IACH,SAAS,IAAA;IACT,KAAK,IAAA;IACL,QAAQ,IAAA;IACR,SAAS,IAAA;IACT,QAAQ,IAAA;IACR,IAAI,KAAA;IACJ,MAAM,KAAA;IACN,KAAK,KAAA;IACL,KAAK,KAAA;IACL,IAAI,KAAA;IACJ,UAAU,KAAA;IACV,OAAO,KAAA;IACP,QAAQ,KAAA;IACR,UAAU,KAAA;CACX;AAED,MAAM,MAAM,gBAAgB;IAC1B,mBAAmB,IAAA;IACnB,kBAAkB,IAAA;IAClB,qBAAqB,IAAA;CACtB;AAED,MAAM,MAAM,cAAc;IACxB,aAAa,IAAA;IACb,UAAU,IAAA;IACV,aAAa,IAAA;IACb,SAAS,IAAA;IACT,aAAa,IAAA;IACb,WAAW,IAAA;IACX,UAAU,IAAA;IACV,mBAAmB,IAAA;IACnB,iBAAiB,IAAA;IACjB,iBAAiB,IAAA;IACjB,WAAW,KAAA;IACX,YAAY,KAAA;IACZ,UAAU,KAAA;IACV,MAAM,KAAA;CACP;AAED,MAAM,MAAM,iBAAiB;IAC3B,aAAa,QAAQ;IACrB,YAAY,OAAO;IACnB,WAAW,MAAM;IACjB,YAAY,OAAO;IACnB,WAAW,IAAM;IACjB,YAAY,OAAO;IACnB,WAAW,MAAM;IACjB,YAAY,OAAO;IACnB,WAAW,IAAI;CAChB", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/recorder/FlutterAudioCaptureRecorder.ts": {"version": 3, "file": "FlutterAudioCaptureRecorder.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/recorder/FlutterAudioCaptureRecorder.ets"], "names": [], "mappings": "OAeS,KAAK;OACP,EAAoB;cAAd,YAAY;OAClB,EAAE,GAAG,EAAE;OACP,EAAW,gBAAgB,EAAE;cAA3B,OAAO;cACP,4BAA4B,QAAQ,wCAAwC;cAC5E,SAAS,QAAQ,aAAa;AAEvC,MAAM,GAAG,GAAG,6BAA6B,CAAA;AAEzC,MAAM,OAAO,2BAA4B,YAAW,SAAS;IAC3D,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,iBAAiB,CAAC,GAAG;QACtE,IAAI,EAAE,KAAK,CAAC,iBAAiB,CAAC,gBAAgB;QAC9C,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;QAChD,KAAK,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB;KACjD,CAAA;IACD,OAAO,CAAC,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,KAAK,CAAC,YAAY,CAAC,GAAG;QAC9D,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,SAAS;QAC/B,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACjC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACjC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;QACjC,EAAE,EAAE,KAAK,CAAC,YAAY,CAAC,UAAU;KAClC,CAAA;IAED,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,CAAA;IACnD,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAA;IAExC,OAAO,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAA;IAC9B,OAAO,CAAC,QAAQ,EAAE,4BAA4B,GAAG,IAAI,GAAG,IAAI,CAAC;IAE7D,YAAY,EAAE,EAAE,4BAA4B;QAC1C,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EACtG,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,GAAG,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAEpG,aAAa;QACb,IAAI,YAAY,GAAG,2BAA2B,CAAC,aAAa,CAAC,UAAU,CAAC,CAAC;QACzE,YAAY,GAAG,CAAC,YAAY,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,CAAC,CAAC,YAAY,CAAC;QAEtG,IAAI,eAAe,EAAE,KAAK,CAAC,eAAe,GAAG;YAC3C,YAAY,EAAE,YAAY;YAC1B,QAAQ,EAAE,2BAA2B,CAAC,UAAU,CAAC,WAAW,CAAC;YAC7D,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC,mBAAmB;YACzD,YAAY,EAAE,KAAK,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,OAAO;SAChE,CAAA;QACD,IAAI,iBAAiB,EAAE,KAAK,CAAC,iBAAiB,GAAG;YAC/C,MAAM,EAAE,KAAK,CAAC,UAAU,CAAC,eAAe;YACxC,aAAa,EAAE,CAAC;SACjB,CAAA;QACD,IAAI,oBAAoB,EAAE,KAAK,CAAC,oBAAoB,GAAG;YACrD,UAAU,EAAE,eAAe;YAC3B,YAAY,EAAE,iBAAiB;SAChC,CAAA;QACD,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,yBAAyB,CAAC,CAAC;YACtC,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;YAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;SACtB;QACD,IAAI,CAAC,QAAQ,GAAG,MAAM,KAAK,CAAC,mBAAmB,CAAC,oBAAoB,CAAC,CAAC;QACtE,IAAI,CAAC,wBAAwB,EAAE,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,OAAO;SACR;QACD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAC;QAC9B,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC/B,CAAC;IAED,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;QACnC,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,OAAO,KAAK,CAAC;SACd;QACD,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC;IAEd,CAAC;IAED,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;QACpC,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,OAAO,KAAK,CAAC;SACd;QACD,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC;QAC5B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,IAAI,KAAK,IAAI,gBAAgB;QAC3B,QAAQ,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE;YAC5B,KAAK,KAAK,CAAC,UAAU,CAAC,SAAS,CAAC;YAChC,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc,CAAC;YACrC,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;gBACjC,OAAO,gBAAgB,CAAC,qBAAqB,CAAC;YAChD,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;YACpC,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;YACpC,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc;gBAClC,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;YAC9C,KAAK,KAAK,CAAC,UAAU,CAAC,YAAY;gBAChC,OAAO,gBAAgB,CAAC,kBAAkB,CAAC;YAC7C;gBACE,OAAO,gBAAgB,CAAC,mBAAmB,CAAC;SAC/C;IACH,CAAC;IAED,OAAO,CAAC,wBAAwB,IAAI,IAAI;QACtC,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,UAAU,EAAE,EAAE;YACjE,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,eAAe,GAAG,KAAK,CAAC,CAAA;YACnC,QAAQ,KAAK,EAAE;gBACb,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;oBACjC,MAAM;gBACR,KAAK,KAAK,CAAC,UAAU,CAAC,SAAS;oBAC7B,MAAM;gBACR,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc;oBAClC,MAAM,IAAI,CAAC,QAAQ,EAAE,KAAK,EAAE,CAAC;oBAC7B,MAAM;gBACR,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa,CAAC;gBACpC,KAAK,KAAK,CAAC,UAAU,CAAC,cAAc;oBAClC,MAAM;gBACR,KAAK,KAAK,CAAC,UAAU,CAAC,YAAY;oBAChC,MAAM;gBACR,KAAK,KAAK,CAAC,UAAU,CAAC,aAAa;oBACjC,MAAM;aACT;QACH,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,EAAE;YACpD,IAAI,OAAO,EAAE,YAAY,GAAG;gBAC1B,MAAM,EAAE,IAAI,CAAC,UAAU;gBACvB,MAAM,EAAE,MAAM,CAAC,UAAU;aAC1B,CAAA;YACD,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAA;YACjD,IAAI,CAAC,UAAU,IAAI,MAAM,CAAC,UAAU,CAAA;QACtC,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;QACtC,sCAAsC;QACtC,OAAO,OAAO,CAAC,CAAC,2BAA2B;IAC7C,CAAC;CAEF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/recorder/FlutterAVRecorder.ts": {"version": 3, "file": "FlutterAVRecorder.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/recorder/FlutterAVRecorder.ets"], "names": [], "mappings": "cAcS,SAAS,QAAQ,aAAa;OAC9B,KAAK;OACP,EAAE;OACF,EAAE,GAAG,EAAE;cACL,aAAa,IAAb,aAAa;OACf,EAAW,gBAAgB,EAAE;cAA3B,OAAO;cACP,4BAA4B,QAAQ,wCAAwC;AAErF,MAAM,GAAG,GAAG,mBAAmB,CAAA;AAC/B,MAAM,OAAO,iBAAkB,YAAW,SAAS;IACjD,OAAO,CAAC,UAAU,EAAE,KAAK,CAAC,UAAU,GAAG,IAAI,GAAG,IAAI,CAAA;IAClD,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,IAAI,GAAG,IAAI,GAAG,IAAI,CAAA;IACxC,OAAO,CAAC,SAAS,EAAE,KAAK,CAAC,iBAAiB,GAAG,IAAI,GAAG,IAAI,CAAA;IACxD,OAAO,CAAC,QAAQ,EAAE,KAAK,CAAC,gBAAgB,GAAG,IAAI,GAAG,IAAI,CAAA;IACtD,OAAO,CAAC,QAAQ,EAAE,4BAA4B,GAAG,IAAI,GAAG,IAAI,CAAC;IAE7D,YAAY,EAAE,EAAE,4BAA4B;QAC1C,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;IACrB,CAAC;IAED,IAAI,KAAK,IAAI,gBAAgB;QAC3B,QAAQ,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE;YAC9B,KAAK,MAAM,CAAC;YACZ,KAAK,UAAU,CAAC;YAChB,KAAK,UAAU,CAAC;YAChB,KAAK,OAAO,CAAC;YACb,KAAK,SAAS;gBACZ,OAAO,gBAAgB,CAAC,mBAAmB,CAAA;YAC7C,KAAK,QAAQ;gBACX,OAAO,gBAAgB,CAAC,kBAAkB,CAAA;YAC5C,KAAK,SAAS;gBACZ,OAAO,gBAAgB,CAAC,qBAAqB,CAAA;YAC/C;gBACE,OAAO,gBAAgB,CAAC,mBAAmB,CAAA;SAC9C;IACH,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EACxE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;QACjE,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC,QAAQ,CAAC,IAAI,EAAE,EAAE,CAAC,QAAQ,CAAC,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAChF,IAAI,CAAC,SAAS,GAAG;YACf,YAAY,EAAE,OAAO;YACrB,aAAa,EAAE,WAAW;YAC1B,eAAe,EAAE,UAAU;YAC3B,UAAU,EAAE,KAAK,CAAC,aAAa,CAAC,SAAS;YACzC,UAAU,EAAE,KAAK,CAAC,mBAAmB,CAAC,WAAW,CAAC,4BAA4B;SAC/E,CAAA;QACD,IAAI,CAAC,QAAQ,GAAG;YACd,eAAe,EAAE,KAAK,CAAC,eAAe,CAAC,qBAAqB;YAC5D,OAAO,EAAE,IAAI,CAAC,SAAS;YACvB,GAAG,EAAE,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ,EAAE;SAC5C,CAAA;QACD,IAAG,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1B,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,CAAC;YAChC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;SACxB;QACD,IAAI,CAAC,UAAU,GAAG,MAAM,KAAK,CAAC,gBAAgB,EAAE,CAAC;QACjD,IAAI,CAAC,qBAAqB,EAAE,CAAC;QAC7B,MAAM,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC7C,MAAM,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,qBAAqB,IAAI,IAAI;QACnC,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,aAAa,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,eAAe,EAAE,MAAM,EAAE,KAAK,CAAC,iBAAiB,EAAE,EAAE;YACzG,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,eAAe,GAAG,KAAK,GAAG,WAAW,GAAG,MAAM,CAAC,CAAA;YAC1D,QAAQ,KAAK,EAAE;gBACb,KAAK,MAAM;oBACT,MAAK;gBACP,KAAK,UAAU;oBACb,MAAK;gBACP,KAAK,UAAU,CAAC;gBAChB,KAAK,OAAO,CAAC;gBACb,KAAK,SAAS;oBACZ,MAAK;gBACP,KAAK,QAAQ;oBACX,MAAK;gBACP,KAAK,SAAS;oBACZ,MAAK;aACR;QACH,CAAC,CAAC,CAAA;QACF,IAAI,CAAC,UAAU,EAAE,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,aAAa,EAAE,EAAE;YAClD,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oCAAoC,GAAG,GAAG,CAAC,IAAI,GAAG,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,CAAA;YACtF,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAA;QAC1B,CAAC,CAAC,CAAA;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC;QACnC,IAAG,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;YACjC,OAAO,KAAK,CAAC;SACd;QACD,MAAM,IAAI,CAAC,UAAU,EAAE,KAAK,EAAE,CAAC;QAC/B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;QACpC,IAAG,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;YACjC,OAAO,KAAK,CAAC;SACd;QACD,MAAM,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,CAAC;QAChC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC;QAC/B,IAAG,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1B,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,oBAAoB,CAAC,CAAC;YACjC,OAAO;SACR;QACD,IAAI;YACF,MAAM,IAAI,CAAC,UAAU,EAAE,IAAI,EAAE,CAAC;YAC9B,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC;YACpC,IAAI,CAAC,UAAU,EAAE,GAAG,CAAC,OAAO,CAAC,CAAC;YAC9B,MAAM,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,CAAC;YACjC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;YACvB,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;SAC9B;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,wBAAwB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAC5D;IACH,CAAC;IAED,KAAK,CAAC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC;QACtC,IAAG,IAAI,CAAC,UAAU,IAAI,IAAI,EAAE;YAC1B,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,4BAA4B,EAAE,CAAC;SAC7D;aAAM;YACL,OAAO,CAAC,CAAC;SACV;IACH,CAAC;CAEF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/recorder/FlutterRecorder.ts": {"version": 3, "file": "FlutterRecorder.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/recorder/FlutterRecorder.ets"], "names": [], "mappings": "OAcO,EAAE,GAAG,EAAE;cACL,4BAA4B,QAAQ,wCAAwC;OAC9E,EAAkB,OAAO,EAAE,gBAAgB,EAAE;cAA3C,cAAc;OAChB,EAAE,2BAA2B,EAAE;OAC/B,EAAE,iBAAiB,EAAE;cACnB,SAAS,QAAQ,aAAa;OAC9B,cAAc;OAChB,EAAE;AAET,MAAM,GAAG,GAAG,iBAAiB,CAAA;AAC7B,MAAM,OAAO,eAAe;IAC1B,OAAO,CAAC,QAAQ,EAAE,SAAS,GAAG,IAAI,GAAG,IAAI,CAAC;IAC1C,MAAM,CAAC,UAAU,EAAE,4BAA4B,GAAG,IAAI,GAAG,IAAI,CAAC;IAC9D,OAAO,CAAC,MAAM,EAAE,MAAM,GAAG,EAAE,CAAC;IAC5B,OAAO,CAAC,kBAAkB,EAAE,MAAM,GAAG,CAAC,CAAC;IACvC,OAAO,CAAC,UAAU,EAAE,MAAM,GAAG,CAAC,CAAC,CAAC;IAChC,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;IACvB,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;IAC7B,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC;IACvB,OAAO,CAAC,MAAM,EAAE,gBAAgB,GAAG,gBAAgB,CAAC,mBAAmB,CAAC;IAExE,YAAY,QAAQ,EAAE,4BAA4B;QAChD,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IAC7B,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC;QAC3C,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,IAAI,CAAC,CAAC;QAC7C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;QACzC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,mBAAmB,CAAC;QACnD,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,OAAO,GAAG,OAAO;QAChD,QAAQ,KAAK,EAAC;YACZ,KAAK,OAAO,CAAC,MAAM,CAAC;YACpB,KAAK,OAAO,CAAC,KAAK,CAAC;YACnB,KAAK,OAAO,CAAC,QAAQ;gBACnB,OAAO,IAAI,CAAC;YACd;gBACE,OAAO,KAAK,CAAC;SAChB;IACH,CAAC;IAED,MAAM,CAAC,gBAAgB,IAAI,gBAAgB;QACzC,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC;QAC1D,IAAI,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAC1C,IAAG,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC1B,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;YACxB,OAAO,IAAI,CAAC;SACb;aAAM;YACL,OAAO,KAAK,CAAC;SACd;IACH,CAAC;IAED,OAAO,CAAC,UAAU,IAAI,IAAI;QACxB,IAAG,IAAI,CAAC,UAAU,IAAI,CAAC,CAAC,EAAE;YACxB,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAChC;IACH,CAAC;IAED,OAAO,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,GAAG,IAAI;QACtC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,IAAI,CAAC,kBAAkB,GAAG,QAAQ,CAAC;QACnC,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,IAAI,QAAQ,IAAI,CAAC,EAAE;YACzC,OAAO;SACR;QACD,IAAI,CAAC,UAAU,GAAG,WAAW,CAAC,KAAK,IAAI,EAAE;YACrC,IAAI,IAAI,GAAI,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC;YACjH,IAAI,EAAE,GAAG,CAAC,CAAC;YACX,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACxB,IAAI,YAAY,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,eAAe,EAAE,CAAC;gBACzD,IAAI,YAAY,GAAG,UAAU,CAAC;gBAC9B,IAAI,CAAC,GAAG,YAAY,GAAG,YAAY,CAAC;gBACpC,IAAI,EAAE,GAAG,MAAM,CAAC;gBAChB,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC7B,IAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE;oBACvB,EAAE,GAAG,CAAC,CAAC;iBACR;aACF;YACD,IAAI,CAAC,UAAU,EAAE,iCAAiC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC;QACjE,CAAC,EAAE,QAAQ,CAAC,CAAC;IACf,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAChG,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;QAChF,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC1B,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,KAAK,IAAI,OAAO,CAAC,KAAK,EAAE;YAC1B,IAAI,CAAC,QAAQ,GAAG,IAAI,2BAA2B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SACnE;aAAM;YACL,IAAI,CAAC,QAAQ,GAAG,IAAI,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;SACzD;QACD,IAAI,CAAC,MAAM,GAAG,EAAE,CAAC;QACjB,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;QACjC,IAAI;YACF,MAAM,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YACnG,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;YACnF,IAAG,IAAI,CAAC,kBAAkB,GAAG,CAAC,EAAE;gBAC9B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;aACxC;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,0BAA0B,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;YAC7D,OAAO,KAAK,CAAC;SACd;QACD,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;QACrD,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC,IAAI,CAAC,CAAC;QAC9C,OAAO,IAAI,CAAC;IACd,CAAC;IAED,MAAM,CAAC,aAAa,CAAC,IAAI,EAAE,WAAW,GAAG,IAAI;QAC3C,IAAI,CAAC,UAAU,EAAE,aAAa,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,YAAY,IAAI,OAAO,CAAC,IAAI,CAAC;QACxC,MAAM,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,IAAI,CAAC,UAAU,EAAE,qBAAqB,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;IAC5D,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,aAAa,IAAI,OAAO,CAAC,IAAI,CAAC;QACzC,IAAI,CAAC,UAAU,EAAE,CAAC;QAClB,MAAM,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;QACnC,IAAI,CAAC,eAAe,GAAG,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxF,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,kBAAkB,CAAC;QAClD,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC,IAAI,CAAC,CAAC;IAChD,CAAC;IAED,MAAM,CAAC,KAAK,CAAC,cAAc,IAAI,OAAO,CAAC,IAAI,CAAC;QAC1C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;QACvC,MAAM,IAAI,CAAC,QAAQ,EAAE,YAAY,EAAE,CAAC;QACpC,IAAG,IAAI,CAAC,eAAe,IAAI,CAAC,EAAE;YAC5B,IAAI,CAAC,UAAU,IAAI,cAAc,CAAC,SAAS,CAAC,cAAc,CAAC,QAAQ,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC;SAC5G;QACD,IAAI,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC;QAC1B,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;QACrD,IAAI,CAAC,UAAU,EAAE,uBAAuB,CAAC,IAAI,CAAC,CAAC;IACjD,CAAC;IAED,MAAM,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,GAAG,IAAI;QACxD,IAAI,CAAC,kBAAkB,GAAG,cAAc,CAAC;QACzC,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;YACxB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC;SACxC;IACH,CAAC;IAED,MAAM,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,GAAG,MAAM;QAC1C,OAAO,UAAU,CAAC,IAAI,CAAC,CAAC,QAAQ,GAAI,GAAG,GAAG,OAAO,CAAC;IACpD,CAAC;IAED,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,MAAM;QACnC,IAAG,IAAI,IAAI,IAAI,EAAE;YACf,OAAO,EAAE,CAAC;SACX;aAAM;YACL,IAAI,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;YACjC,IAAG,CAAC,OAAO,EAAE;gBACX,IAAI,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;aAChC;YACD,OAAO,IAAI,CAAC;SACb;IACH,CAAC;IAED,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,OAAO,CAAC,IAAI,CAAC;QAEjC,IAAI;YACF,IAAI,CAAC,UAAU,EAAE,CAAC;YAClB,IAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,EAAE;gBACxB,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,CAAC;aAClC;SACF;QAAC,OAAO,GAAG,EAAE;YACZ,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;SAClD;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,MAAM,GAAG,gBAAgB,CAAC,mBAAmB,CAAC;IACrD,CAAC;CACF", "entry-package-info": "flutter_sound|1.0.0"}, "flutter_sound|flutter_sound|1.0.0|src/main/ets/components/recorder/IRecorder.ts": {"version": 3, "file": "IRecorder.ets", "sourceRoot": "", "sources": ["ohos/src/main/ets/components/recorder/IRecorder.ets"], "names": [], "mappings": "cAcS,OAAO,EAAE,gBAAgB,QAAQ,6BAA6B;AAEvE,MAAM,WAAW,SAAS;IACxB,IAAI,KAAK,IAAI,gBAAgB,CAAA;IAC7B,WAAW,CAAC,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAClE,KAAK,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,WAAW,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAA;IACnE,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,CAAA;IAC3B,WAAW,IAAI,OAAO,CAAC,OAAO,CAAC,CAAA;IAC/B,YAAY,IAAI,OAAO,CAAC,OAAO,CAAC,CAAA;IAChC,eAAe,IAAI,OAAO,CAAC,MAAM,CAAC,CAAA;CACnC", "entry-package-info": "flutter_sound|1.0.0"}}