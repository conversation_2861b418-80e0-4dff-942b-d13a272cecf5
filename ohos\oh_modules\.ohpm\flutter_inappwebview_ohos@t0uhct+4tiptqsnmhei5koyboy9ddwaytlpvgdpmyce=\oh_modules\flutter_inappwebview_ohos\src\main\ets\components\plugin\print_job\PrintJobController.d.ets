import { Any } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Disposable } from '../types/Disposable';
import PrintJobInfoExt from '../types/PrintJobInfoExt';
import PrintJobChannelDelegate from './PrintJobChannelDelegate';
import PrintJobSettings from './PrintJobSettings';
export default class PrintJobController implements Disposable {
    protected LOG_TAG: string;
    METHOD_CHANNEL_NAME_PREFIX: string;
    id: string;
    plugin: InAppWebViewFlutterPlugin | null;
    channelDelegate: PrintJobChannelDelegate | null;
    job: Any | null;
    settings: PrintJobSettings | null;
    constructor(id: string, job: Any, settings: PrintJobSettings, plugin: InAppWebViewFlutterPlugin);
    cancel(): void;
    restart(): void;
    getInfo(): PrintJobInfoExt | null;
    disposeNoCancel(): void;
    dispose(): void;
}
