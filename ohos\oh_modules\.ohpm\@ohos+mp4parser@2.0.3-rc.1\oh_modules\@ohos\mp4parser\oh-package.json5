{"types": "", "keywords": ["OpenHarmony", "mp4parser", "HarmonyOS"], "author": "ohos_tpc", "ohos": {"org": "opensource"}, "description": "一个读取、写入操作音视频文件编辑的工具。支持视频裁剪、视频合成、音频裁剪、音频合成、视频取帧。", "main": "index.ets", "repository": "https://gitee.com/openharmony-tpc/mp4parser", "version": "2.0.3-rc.1", "tags": ["mp4parser", "Media"], "dependencies": {"@types/libmp4parser_napi.so": "file:./src/main/cpp/types/libmp4parser_napi"}, "license": "Apache License 2.0", "devDependencies": {}, "name": "@ohos/mp4parser", "metadata": {"sourceRoots": ["./src/main"]}, "compatibleSdkVersion": 9, "compatibleSdkType": "OpenHarmony", "obfuscated": false, "nativeComponents": [{"name": "libmp4parser_napi.so", "compatibleSdkVersion": 9, "compatibleSdkType": "OpenHarmony"}]}