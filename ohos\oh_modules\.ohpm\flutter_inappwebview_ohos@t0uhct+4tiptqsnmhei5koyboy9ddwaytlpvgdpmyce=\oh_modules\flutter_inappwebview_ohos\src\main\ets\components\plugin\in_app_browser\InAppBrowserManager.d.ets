import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import { Any, MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import UIAbility from "@ohos.app.ability.UIAbility";
export default class InAppBrowserManager extends ChannelDelegateImpl {
    plugin: InAppWebViewFlutterPlugin | null;
    id: string;
    static shared: Map<string, InAppBrowserManager>;
    constructor(plugin: InAppWebViewFlutterPlugin);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    open(uiAbility: UIAbility, args: Map<string, Any>): void;
    openWithSystemBrowser(uiAbility: UIAbility, url: string, result: MethodResult): void;
    dispose(): void;
}
