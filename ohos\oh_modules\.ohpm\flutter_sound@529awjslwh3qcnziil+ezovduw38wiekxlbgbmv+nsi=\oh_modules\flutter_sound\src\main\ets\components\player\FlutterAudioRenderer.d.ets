import { IPlayer } from './IPlayer';
import { t_PLAYER_STATE } from '../plugin/FlutterSoundTypes';
import { FlutterSoundPlayerCallback } from '../plugin/FlutterSoundPlayerCallback';
export declare class FlutterAudioRenderer implements IPlayer {
    private static sampleRateMap;
    private static channelMap;
    private renderModel;
    private callback;
    private rendererVolume;
    private rendererSpeed;
    private bufferSize;
    private targetBufferSize;
    private file;
    private writeDataCallback;
    constructor(cb: FlutterSoundPlayerCallback);
    startPlayer(path: string, sampleRate: number, numChannels: number, bufferSize: number, enableVoiceProcessing: boolean): Promise<void>;
    stopPlayer(): Promise<void>;
    pausePlayer(): Promise<void>;
    resumePlayer(): Promise<void>;
    seekTo(milliseconds: number): void;
    setVolume(volume: number): void;
    setSpeed(speed: number): void;
    get duration(): number;
    get position(): number;
    get state(): t_PLAYER_STATE;
    feed(data: ArrayBuffer): number;
    isPlaying(): boolean;
    private setPlayerCallback;
    private setPlayerConfig;
}
