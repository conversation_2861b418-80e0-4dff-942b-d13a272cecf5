import ArrayList from '@ohos.util.ArrayList';
import StandardMessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/StandardMessageCodec';
import { ByteBuffer } from '@ohos/flutter_ohos/src/main/ets/util/ByteBuffer';
import MessageCodec from '@ohos/flutter_ohos/src/main/ets/plugin/common/MessageCodec';
import { BinaryMessenger } from '@ohos/flutter_ohos/src/main/ets/plugin/common/BinaryMessenger';
export declare enum SourceCamera {
    REAR = 0,
    FRONT = 1
}
export declare enum SourceType {
    CAMERA = 0,
    GALLERY = 1
}
export declare enum CacheRetrievalType {
    IMAGE = 0,
    VIDEO = 1
}
export default class Messages {
    static wrapError(exception: Error): ArrayList<ESObject>;
}
export declare class FlutterError extends Error {
    code: string;
    details: ESObject;
    constructor(code: string, message: string, details: ESObject);
}
export declare class GeneralOptions {
    private allowMultiple;
    private usePhotoPicker;
    private constructor();
    getAllowMultiple(): boolean;
    setAllowMultiple(setterArg: boolean): void;
    getUsePhotoPicker(): boolean;
    setUsePhotoPicker(setterArg: boolean): void;
    Builder: ESObject;
    toList(): ArrayList<ESObject>;
    static fromList(list: ArrayList<ESObject>): GeneralOptions;
}
declare class ImageSelectionOptionsBuilder {
    setMaxWidth: (setterArg: number) => ESObject;
    setMaxHeight: (setterArg: number) => ESObject;
    setQuality: (setterArg: number) => ESObject;
    build: () => ESObject;
    constructor(setMaxWidth: (setterArg: number) => ESObject, setMaxHeight: (setterArg: number) => ESObject, setQuality: (setterArg: number) => ESObject, build: () => ESObject);
}
export declare class ImageSelectionOptions {
    private maxWidth;
    private maxHeight;
    private quality;
    private constructor();
    getMaxWidth(): number;
    setMaxWidth(setterArg: number): void;
    getMaxHeight(): number;
    setMaxHeight(setterArg: number): void;
    getQuality(): number;
    setQuality(setterArg: number): void;
    Builder: ImageSelectionOptionsBuilder;
    toList(): ArrayList<ESObject>;
    static fromList(list: ArrayList<ESObject>): ImageSelectionOptions;
}
declare class MediaSelectionOptionsBuilder {
    setImageSelectionOptions: (setterArg: ImageSelectionOptions) => ESObject;
    build: () => ESObject;
    constructor(setImageSelectionOptions: (setterArg: ImageSelectionOptions) => ESObject, build: () => ESObject);
}
export declare class MediaSelectionOptions {
    private imageSelectionOptions;
    static imageSelectionOptions: ImageSelectionOptions | null;
    constructor();
    getImageSelectionOptions(): ImageSelectionOptions | null;
    setImageSelectionOptions(setterArg: ImageSelectionOptions | null): void;
    Builder: MediaSelectionOptionsBuilder;
    toList(): ArrayList<ESObject>;
    fromList(list: ArrayList<ESObject>): MediaSelectionOptions;
}
declare class VideoSelectionOptionsBuilder {
    setMaxDurationSeconds: (setterArg: number) => ESObject;
    build: () => ESObject;
    constructor(setMaxDurationSeconds: (setterArg: number) => ESObject, build: () => ESObject);
}
export declare class VideoSelectionOptions {
    private maxDurationSeconds;
    static maxDurationSeconds: number;
    private constructor();
    getMaxDurationSeconds(): number;
    setMaxDurationSeconds(setterArg: number): void;
    Builder: VideoSelectionOptionsBuilder;
    toList(): ArrayList<ESObject>;
    static fromList(list: ArrayList<ESObject>): VideoSelectionOptions;
}
declare class SourceSpecificationBuilder {
    setType: (setterArg: SourceType) => ESObject;
    setCamera: (setterArg: SourceCamera) => ESObject;
    build: () => ESObject;
    constructor(setType: (setterArg: SourceType) => ESObject, setCamera: (setterArg: SourceCamera) => ESObject, build: () => ESObject);
}
export declare class SourceSpecification {
    private type;
    private camera;
    private constructor();
    getType(): SourceType;
    setType(setterArg: SourceType): void;
    getCamera(): SourceCamera;
    setCamera(setterArg: SourceCamera): void;
    Builder: SourceSpecificationBuilder;
    toList(): ArrayList<ESObject>;
    static fromList(list: ArrayList<ESObject>): SourceSpecification;
}
export declare class CacheRetrievalErrorBuilder {
    setCode: null | ((setterArg: string) => ESObject);
    setMessage: ((setterArg: string) => ESObject) | null;
    build: (() => ESObject) | null;
    constructor(setCode: null | ((setterArg: string) => ESObject), setMessage: ((setterArg: string) => ESObject) | null, build: (() => ESObject) | null);
}
export declare class CacheRetrievalError {
    private code;
    private message;
    constructor();
    getCode(): string;
    setCode(setterArg: string): void;
    getMessage(): string;
    setMessage(setterArg: string): void;
    Builder: CacheRetrievalErrorBuilder;
    toList(): ArrayList<ESObject>;
    fromList(list: ArrayList<ESObject>): CacheRetrievalError;
}
export declare class CacheRetrievalResultBuilder {
    private type;
    private error;
    private paths;
    setType: ((setterArg: CacheRetrievalType) => ESObject) | null;
    setError: ((setterArg: CacheRetrievalError) => ESObject) | null;
    setPaths: ((setterArg: ArrayList<string>) => ESObject) | null;
    build: (() => ESObject) | null;
    constructor(setType: ((setterArg: CacheRetrievalType) => ESObject) | null, setError: ((setterArg: CacheRetrievalError) => ESObject) | null, setPaths: ((setterArg: ArrayList<string>) => ESObject) | null, build: (() => ESObject) | null);
}
export declare class CacheRetrievalResult {
    private type;
    private error;
    private paths;
    constructor();
    getType(): CacheRetrievalType;
    setType(setterArg: CacheRetrievalType): void;
    getError(): CacheRetrievalError | null;
    setError(setterArg: CacheRetrievalError | null): void;
    getPaths(): ArrayList<string>;
    setPaths(setterArg: ArrayList<string>): void;
    Builder: CacheRetrievalResultBuilder;
    toList(): ArrayList<ESObject>;
    fromList(list: ArrayList<ESObject>): CacheRetrievalResult;
}
export interface Result<T> {
    success(result: T): void;
    error(error: Error | ESObject): Error | ESObject;
}
export declare class ImagePickerApiCodec extends StandardMessageCodec {
    static INSTANCE: ImagePickerApiCodec;
    private constructor();
    readValueOfType(type: number, buffer: ByteBuffer): ESObject;
    writeValue(stream: ByteBuffer, value: ESObject): void;
}
export declare abstract class ImagePickerApi {
    abstract pickImages(source: SourceSpecification, options: ImageSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    abstract pickVideos(source: SourceSpecification, options: VideoSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    abstract pickMedia(mediaSelectionOptions: MediaSelectionOptions, generalOptions: GeneralOptions, result: Result<ArrayList<string>>): void;
    abstract retrieveLostResults(): Promise<CacheRetrievalResult>;
    static getCodec(): MessageCodec<ESObject>;
    static setup(binaryMessenger: BinaryMessenger | null, api?: ImagePickerApi | null): void;
}
export {};
