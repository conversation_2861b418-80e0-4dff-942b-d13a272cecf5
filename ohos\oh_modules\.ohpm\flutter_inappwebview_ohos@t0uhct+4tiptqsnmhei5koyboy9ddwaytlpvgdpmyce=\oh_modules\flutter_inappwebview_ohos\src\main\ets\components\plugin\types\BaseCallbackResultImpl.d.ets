import { Any } from '@ohos/flutter_ohos';
import ICallbackResult from './ICallbackResult';
export default class BaseCallbackResultImpl<T> implements ICallbackResult<T> {
    nonNullSuccess(result: T): boolean;
    nullSuccess(): boolean;
    defaultBehaviour(result: T | null): void;
    success(obj: Any): void;
    decodeResult(obj: Any): T | null;
    error(errorCode: string, errorMessage: string, errorDetails: Any): void;
    notImplemented(): void;
}
