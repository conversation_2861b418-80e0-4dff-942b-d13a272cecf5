import { PlatformView, PlatformViewFactory } from '@ohos/flutter_ohos';
import InAppWebViewFlutterPlugin from '../InAppWebViewFlutterPlugin';
import { Any } from '@ohos/flutter_ohos';
export default class FlutterWebViewFactory extends PlatformViewFactory {
    static VIEW_TYPE_ID: string;
    private plugin;
    constructor(plugin: InAppWebViewFlutterPlugin);
    create(context: Context, id: number, args: Any): PlatformView;
}
