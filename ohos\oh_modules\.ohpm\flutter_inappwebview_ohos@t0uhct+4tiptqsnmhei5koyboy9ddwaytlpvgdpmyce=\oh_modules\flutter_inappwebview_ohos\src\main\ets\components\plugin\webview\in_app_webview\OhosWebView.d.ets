import web_webview from '@ohos.web.webview';
import { Params } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import InAppWebView from './InAppWebView';
@Component
export declare struct OhosWebView {
    @Prop
    params: Params;
    controller: web_webview.WebviewController;
    inAppWebView: InAppWebView | null;
    @State
    isRefreshing: boolean;
    @State
    isEnableRefresh: boolean;
    @State
    startScripts: Array<ScriptItem>;
    @State
    cacheEnabled: boolean;
    aboutToAppear(): void;
    aboutToDisappear(): void;
    build(): void;
    @Builder
    buildWeb(): void;
    private setPathAllowingUniversalAccess;
}
@Builder
export declare function buildOhosWebView(params: Params): void;
