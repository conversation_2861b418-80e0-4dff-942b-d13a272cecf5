import URLCredential from '../types/URLCredential';
import CredentialDatabaseHelper from './CredentialDatabaseHelper';
import List from '@ohos.util.List';
export default class URLCredentialDao {
    credentialDatabaseHelper: CredentialDatabaseHelper;
    projection: string[];
    constructor(credentialDatabaseHelper: CredentialDatabaseHelper);
    getAllByProtectionSpaceId(protectionSpaceId: number): Promise<List<URLCredential>>;
    find(username: string, password: string, protectionSpaceId: number): Promise<URLCredential>;
    insert(urlCredential: URLCredential): Promise<number>;
    update(urlCredential: URLCredential): Promise<number>;
    delete(urlCredential: URLCredential): Promise<number>;
}
