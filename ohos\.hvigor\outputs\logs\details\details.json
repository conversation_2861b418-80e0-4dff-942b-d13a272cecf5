{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": false, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"PreBuild": 186366100, "CreateModuleInfo": 2227700, "ConfigureCmake": 521000, "MergeProfile": 123467300, "CreateBuildProfile": 3332500, "PreCheckSyscap": 527600, "ProcessIntegratedHsp": 2659300, "BuildNativeWithCmake": 544000, "MakePackInfo": 14052800, "SyscapTransform": 19026400, "ProcessProfile": 116143200, "ProcessRouterMap": 27850600, "BuildNativeWithNinja": 1459500, "ProcessResource": 15776900, "GenerateLoaderJson": 130337000, "ProcessLibs": 1623200600, "CompileResource": 3759413400, "BuildJS": 7527600, "CompileArkTS": 17803257700, "GeneratePkgModuleJson": 2290600, "PackageHap": 2765785400, "SignHap": 10103712300, "CollectDebugSymbol": 5418400, "assembleHap": 227800}}, "BUILD_ID": "202507311401045290", "TOTAL_TIME": 38490910100}}