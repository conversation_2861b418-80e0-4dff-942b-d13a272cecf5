import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:lib_base/config/route_name.dart';
import 'package:lib_base/config/route_utils.dart';
import 'package:lib_base/config/theme_config.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/feedback_page_param.dart';
import 'package:lib_base/model/http/book_info_detail.dart';
import 'package:lib_base/model/http/video_info_model.dart';
import 'package:lib_base/model/sing_sound_result_info.dart';
import 'package:lib_base/resource/common_constant.dart';
import 'package:lib_base/model/unit_dubbing_model.dart';
import 'package:lib_base/utils/business/dubbing_util.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/ui_util.dart';
import 'package:lib_base/widgets/dialog/single_button_dialog.dart';
import 'package:lib_base/widgets/rating/rating_view.dart';
import 'package:lib_base/widgets/video/simple_video_player/simple_video_player.dart';
import 'package:yyb_text_dubbing/model/dubbing_result_item.dart';
import 'package:yyb_text_dubbing/model/http/dubbing_video_info_model.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/widgets/text_dubbing_result_item.dart';
import 'package:yyb_text_dubbing/src/extension/build_context_ext.dart';

import 'package:yyb_text_dubbing/config/router.dart';
import 'package:lib_base/model/text_dubbing_detail_model.dart';
import 'package:lib_base/model/text_dubbing_dubbing_param.dart';
import 'package:yyb_text_dubbing/providers/text_dubbing_dubbing_provider.dart';
import 'package:yyb_text_dubbing/src/utils/asset_utils.dart';
import 'package:yyb_text_dubbing/src/generated/assets.dart';
import 'package:dotted_line/dotted_line.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/widgets/dubbing_control_view.dart';
import 'package:video_player/video_player.dart';

import 'package:yyb_text_dubbing/util/text_dubbing_file_util.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/controllers/text_dubbing_record_provider.dart';
import 'package:yyb_text_dubbing/page/text_dubbing_dubbing/model/text_dubbing_review_param.dart';

class TextDuddingDubbingView extends StatelessWidget {
  final TextDubbingDubbingProvider notifier;
  final TextDubbingRecordProvider controller;
  final TextDubbingDetailModel dubbingDetailModel;
  final List<DubbingVideoInfoModel> videos;
  final void Function(
          TextDubbingResultItem resultItem, DubbingVideoInfoModel model)
      itemOnTap; //评分页面
  final ValueChanged<VideoPlayerController>? doPlay;
  final TextDubbingDubbingParam param;
  const TextDuddingDubbingView({
    super.key,
    required this.notifier,
    required this.controller,
    required this.dubbingDetailModel,
    required this.videos,
    required this.itemOnTap,
    required this.doPlay,
    required this.param,
  });

  @override
  Widget build(BuildContext context) {
    //第一处配音的起点
    Duration? startPoint;
    if (videos.isNotEmpty) {
      var seconds = double.tryParse(videos[0].startPoint ?? "");
      if (seconds != null) {
        int millseconds = (seconds * 1000).toInt();
        startPoint = Duration(milliseconds: millseconds);
      }
    }

    return Column(
      children: [
        Container(
          width: 1.sw,
          height: MediaQuery.of(context).padding.top,
          decoration: BoxDecoration(color: Colors.white),
        ),
        Stack(
          children: [
            //播放器
            // Container(
            //   height: 230.r,
            //   child: NormalVideoPlayer(
            //     videoImageUrl: ImageUtil.getImageUrl(dubbingDetailModel.dubbingResoure?.videoCove??""),
            //     videoUrl: ImageUtil.getImageUrl(dubbingDetailModel.dubbingResoure?.originalAudio??""),
            //   ),
            // ),
            SimpleVideoPlayer(
              position: startPoint,
              videoUrl: ImageUtil.getImageUrl(
                  dubbingDetailModel.dubbingResoure?.originalAudio ?? ""),
              videoImageUrl: ImageUtil.getImageUrl(
                  dubbingDetailModel.dubbingResoure?.videoCove ?? ""),
              autoPlay: true,
              width: 1.sw,
              height: 230.r,
              controller: controller.mVideoPlayerController,
              doPlay: doPlay,
              moduleId: "",
              subject: CommonConstant.BZZY_ENGLISH,
            ),
            Positioned(
              left: 0,
              top: 0,
              child: InkWell(
                onTap: () => back(),
                child: Container(
                  margin: EdgeInsets.all(10.r),
                  padding: EdgeInsets.all(10.r),
                  decoration: BoxDecoration(
                    color: Colors.grey.withOpacity(0.5),
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Icon(
                      Icons.arrow_back_ios,
                      color: Colors.white,
                      size: 20.r,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        Container(
          width: 1.sw,
          height: 1.sh -
              230.r -
              60.r -
              MediaQuery.of(context).padding.top -
              MediaQuery.of(context).padding.bottom,
          child: ValueListenableBuilder(
            valueListenable: notifier.videoInfoList,
            builder: (context, value, child) {
              return ListView.separated(
                padding: EdgeInsets.zero,
                itemCount: value.length,
                itemBuilder: (context, index) {
                  DubbingVideoInfoModel model = value[index];
                  return ValueListenableBuilder(
                      valueListenable: notifier.currentDubbing,
                      builder: (_, value, child) {
                        return _DubbingListItem(
                          notifier: notifier,
                          controller: controller,
                          index: index,
                          itemOnTap: itemOnTap,
                        );
                      });
                },
                separatorBuilder: (context, index) {
                  return Divider(
                    height: 1.0,
                    color: Colors.white,
                  );
                },
              );
            },
          ),
        ),
        _botBar(context),
      ],
    );
  }

  Widget _botBar(BuildContext context) {
    return Container(
      width: 1.sw,
      height: MediaQuery.of(context).padding.bottom + 60.r,
      padding: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom),
      decoration: BoxDecoration(color: Colors.white),
      child: Stack(
        alignment: Alignment.center,
        children: [
          GestureDetector(
            // onTap: () async {
            //
            // },
            onTap: () async {
              //弹窗提示重新上传
              // showSmartDialog(
              //   Text("录音上传失败", style: TextStyle(color: ThemeConfig.currentTheme.colorTextPrimary3,fontSize: 16.sp),),
              //
              // );
              // return;
              //判断是否全部完成了配音
              if (notifier.dubbingResultMap.length <
                  notifier.videoInfoList.value.length) {
                var str = "请完成所有条目测评在进行操作。未练习的条目：";
                for (int i = 0; i < notifier.videoInfoList.value.length; i++) {
                  DubbingVideoInfoModel model = notifier.videoInfoList.value[i];
                  if (notifier.dubbingResultMap[model.id ?? ""] == null) {
                    str += "第${i + 1}条，";
                  }
                }
                str = str.substring(0, str.length - 1);
                showSmartDialog(SingleButtonDialog(
                  title: "温馨提示",
                  titleColor: ThemeConfig.currentTheme.colorDesignPrimary3,
                  desc: Text(
                    str,
                    style: TextStyle(
                        color: ThemeConfig.currentTheme.colorTextPrimary3,
                        fontSize: 15.sp),
                  ),
                  confirmText: "我知道了",
                  confirm: () {
                    dismissDialog();
                  },
                ));
              } else {
                //全部完成了，提交
                controller.mVideoPlayerController.videoController?.pause();
                if (!notifier.singSoundDubbingController.isRecording) {
                  VideoInfoModel? model =
                      await notifier.mergeRecordAudioAndBackGroundAudio(
                          dubbingDetailModel.dubbingResoure?.videoCove ?? "");
                  if (model != null) {
                    // widget.controller.scrollToItem(model);
                  }
                  Logger.info("配音合并完成，跳转到预览界面 notifier.resourceId:${ notifier.resourceId}");
                  //预览界面
                  String mergedFolder =
                      await TextDubbingFileUtil.audioVideoMergeFolderPath(
                          notifier.resourceId);
                  String dubbingVideo =
                      "$mergedFolder/${TextDubbingFileUtil.dubbingVideoFileName}";

                  List<TextDubbingResultItem> resultItems =
                      notifier.getDubbingResultItemList();
                  DubbingResultItem totalResultItem =
                      notifier.getDubbingTotalResultItem();
                  //停止视频播放
                  // controller.mVideoPlayerController;
                  notifier.pauseAll();
                  String resourcePath =
                      await TextDubbingFileUtil.resourceFolderPath(
                          notifier.resourceId);
                  String audioFilePath =
                      "$resourcePath/${TextDubbingFileUtil.originAudioFileName}";
                  TextDubbingReviewParam reviewParam = TextDubbingReviewParam(
                    videoUrl: dubbingVideo,
                    audioFilePath: audioFilePath,
                    dubbingDetailModel: dubbingDetailModel,
                    videoInfoList: notifier.videoInfoList.value,
                    dubbingResultMap: notifier.dubbingResultMap,
                    resultItems: resultItems,
                    totalResultItem: totalResultItem,
                    chapterModel: param.chapterModel,
                    videoName: param.videoName,
                    book: param.book,
                  );
                  Logger.info(
                      "============ videoFile:$dubbingVideo, file exist:${File(dubbingVideo).existsSync()}");
                  // toPage(TextDubbingRouteName.textDubbingReview,
                  //     extra: reviewParam);
                }
              }
              // notifier.submitResult();
              //
            },
            child: Container(
              width: 200.r,
              height: 40.r,
              decoration: BoxDecoration(
                  color: ThemeConfig.currentTheme.colorDesignPrimary3,
                  borderRadius: BorderRadius.all(Radius.circular(20.r))),
              child: Center(
                child: Text(
                  context.string.textDubbingylpy,
                  style: TextStyle(color: Colors.white, fontSize: 16.sp),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _DubbingListItem extends StatelessWidget {
  final TextDubbingDubbingProvider notifier;
  final TextDubbingRecordProvider controller;
  final int index;
  final void Function(
          TextDubbingResultItem resultItem, DubbingVideoInfoModel model)
      itemOnTap; //评分页面
  const _DubbingListItem(
      {required this.notifier,
      required this.controller,
      required this.index,
      required this.itemOnTap});

  @override
  Widget build(BuildContext context) {
    DubbingVideoInfoModel model = notifier.videoInfoList.value[index];
    bool isCurrent = model.id == notifier.currentDubbing.value.id;
    return GestureDetector(
      onTap: () {
        if (!isCurrent) {
          //点击的不是当前item，则选中
          //判断上一个录音了完成了没有，完成了才能进行下一个
          if (index > 0) {
            DubbingVideoInfoModel info =
                notifier.videoInfoList.value[index - 1];
            SingsoundResult? result = notifier.dubbingResultMap[info.id ?? ""];
            if (result == null) {
              showToast("请先为上一句录音");
              return;
            }
          }
          notifier.currentDubbing.value = model;
          controller.indexNotifier.value = index;
        }
      },
      child: Container(
        width: 1.sw,
        // height: h,
        padding:
            EdgeInsets.only(left: 15.r, right: 15.r, top: 5.r, bottom: 5.r),
        decoration: BoxDecoration(
            color: isCurrent
                ? Colors.white
                : ThemeConfig.currentTheme.baseBackgroundColor),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 1.sw,
              height: 50.r,
              child: Row(
                children: [
                  RichText(
                      text: TextSpan(
                    text: "",
                    children: [
                      TextSpan(
                          text: "${index + 1}",
                          style: TextStyle(
                              fontSize: 14.sp,
                              color: isCurrent
                                  ? ThemeConfig.currentTheme.colorDesignPrimary3
                                  : ThemeConfig
                                      .currentTheme.colorTextPrimary3)),
                      TextSpan(
                          text: "/${notifier.videoInfoList.value.length}",
                          style: TextStyle(
                              fontSize: 14.sp,
                              color:
                                  ThemeConfig.currentTheme.colorTextPrimary3)),
                    ],
                  )),
                  Expanded(child: Container()),
                  Visibility(
                    visible: isCurrent,
                    child: ValueListenableBuilder(
                      valueListenable:
                          notifier.singsoundResultUpdateIndexNotifier,
                      builder: (context, value, child) {
                        SingsoundResult? result = notifier.dubbingResultMap[
                            notifier.currentDubbing.value.id ?? ""];
                        //1.5颗星星以内需要显示（80分）
                        bool isShow = isCurrent &&
                            (DubbingUtil.getActiveStar(result?.overall ?? 0) <=
                                1.5);
                        if (result == null) isShow = false;
                        return GestureDetector(
                          onTap: () {
                            notifier.pauseAll();
                            TextDubbingResultItem resultItem =
                                notifier.getDubbingResultItem(model);
                            itemOnTap(resultItem, model);
                          },
                          child: _tipsView(isShow),
                        );
                      },
                    ),
                  ),
                  Visibility(
                    visible: isCurrent,
                    child: ValueListenableBuilder(
                      valueListenable:
                          notifier.singsoundResultUpdateIndexNotifier,
                      builder: (context, value, child) {
                        SingsoundResult? result = notifier.dubbingResultMap[
                            notifier.currentDubbing.value.id ?? ""];
                        double activeStar =
                            DubbingUtil.getActiveStar(result?.overall ?? 0);
                        return result == null
                            ? const SizedBox.shrink()
                            : GestureDetector(
                                onTap: () {
                                  notifier.pauseAll();
                                  TextDubbingResultItem resultItem =
                                      notifier.getDubbingResultItem(model);
                                  itemOnTap(resultItem, model);
                                },
                                child: RatingView(
                                    activeStar: activeStar, total: 3),
                              );
                      },
                    ),
                  ),
                  Visibility(
                    visible: isCurrent,
                    child: GestureDetector(
                      onTap: () {
                        notifier.switchTranslateState();
                      },
                      child: Container(
                        width: 40.r,
                        height: 40.r,
                        padding: EdgeInsets.all(5.r),
                        child: ValueListenableBuilder(
                          valueListenable: notifier.translateTypeNotifier,
                          builder: (context, value, child) {
                            return Image(
                              image: AssetImage(AssetsUtils.wrapAsset(
                                  (value == 1)
                                      ? Assets.imagesTdIconFanyiUn
                                      : Assets.imagesTdIconFanyi)),
                            );
                          },
                        ),
                      ),
                    ),
                    // child: IconButton(
                    //   onPressed: (){
                    //     notifier.switchTranslateState();
                    //   },
                    //   icon: Icon(Icons.arrow_back_ios, color: Colors.black,),
                    // ),
                  ),
                ],
              ),
            ),
            ValueListenableBuilder(
              valueListenable: notifier.singsoundResultUpdateIndexNotifier,
              builder: (context, value, child) {
                DubbingVideoInfoModel info =
                    notifier.videoInfoList.value[index];
                SingsoundResult? result =
                    notifier.dubbingResultMap[info.id ?? ""];
                if (result == null) {
                  return Text(
                    model.english ?? "",
                    style: TextStyle(
                        color: ThemeConfig.currentTheme.colorTextPrimary3,
                        fontSize: 18.sp),
                  );
                } else {
                  String html =
                      DubbingUtil.getResultContent(model.english ?? "", result);
                  return HtmlWidget(
                    html,
                    textStyle: TextStyle(fontSize: 18.sp),
                  );
                }
              },
            ),
            SizedBox(
              height: 5.r,
            ),
            ValueListenableBuilder(
                valueListenable: notifier.translateTypeNotifier,
                builder: (context, value, child) {
                  bool res = value == 1 &&
                      (notifier.currentDubbing.value.id == model.id);
                  return Visibility(
                    visible: res,
                    child: Text(
                      model.translation ?? "",
                      style: TextStyle(
                          color: ThemeConfig.currentTheme.colorTextPrimary1,
                          fontSize: 13.sp),
                    ),
                  );
                }),
            SizedBox(
              height: 5.r,
            ),
            Center(
              child: Visibility(
                visible: isCurrent,
                child: DubbingControlView(
                  notifier: notifier,
                  controller: controller,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _tipsView(bool isShow) {
    return Visibility(
      visible: isShow,
      child: Container(
        // decoration: BoxDecoration(color: Color(0xfffe6d50)),
        child: Row(
          children: [
            Container(
              padding: EdgeInsets.only(left: 6.r, right: 0.r),
              height: 20.r,
              decoration: BoxDecoration(
                color: Color(0xfffe6d50),
                borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(2.r),
                    bottomLeft: Radius.circular(2.r)),
              ),
              child: Center(
                child: Text(
                  "评分不高怎么办？",
                  style: TextStyle(color: Colors.white, fontSize: 13.sp),
                ),
              ),
            ),
            Container(
              width: 20.r,
              height: 20.r,
              child: CustomPaint(
                painter: _RianglePainter(),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _audioRecordView(DubbingVideoInfoModel model) {
    int totalSecond = (double.parse(model.endPoint ?? "0") -
            double.parse(model.startPoint ?? "0"))
        .round();
    return Container(
      width: 260.r,
      height: 130.r,
      // decoration: BoxDecoration(color: Colors.blue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          //进度条
          Center(
            child: _LineProgressIndicator(
              totalSecond: totalSecond,
              progress: 0.13,
            ),
          ),
          SizedBox(
            height: 15.r,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _funcItem(),
              SizedBox(
                width: 15.r,
              ),
              _funcItem(),
              SizedBox(
                width: 15.r,
              ),
              _funcItem(),
            ],
          )
        ],
      ),
    );
  }

  Widget _progressBar() {
    return Row(
      children: [
        Container(
          width: 200.r,
          height: 6.r,
          decoration: BoxDecoration(
            color: ThemeConfig.currentTheme.colorDivider2,
            borderRadius: BorderRadius.all(Radius.circular(3.r)),
          ),
        ),
        SizedBox(
          width: 5.r,
        ),
        Text(
          "3s",
          style: TextStyle(
              color: ThemeConfig.currentTheme.colorTextPrimary1,
              fontSize: 14.sp),
        ),
      ],
    );
  }

  Widget _funcItem() {
    return Column(
      children: [
        Image(
          image: AssetImage(
              AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon1)),
          width: 30.r,
        ),
        SizedBox(
          height: 10.r,
        ),
        Text(
          'Play',
          style: TextStyle(
              color: ThemeConfig.currentTheme.colorTextPrimary1,
              fontSize: 14.sp),
        ),
      ],
    );
  }

  Widget _starView() {
    return Container(
      width: 30.r,
      height: 30.r,
      padding: EdgeInsets.all(4.r),
      child: Image(
        image: AssetImage(AssetsUtils.wrapAsset(Assets.imagesTdScoreGrey)),
      ),
    );
  }
}

class _ChapterListView extends StatefulWidget {
  final DubbingModel currentDubbing;
  final UnitDubbingModel chapterModel;
  final void Function(DubbingModel dubbing) dubbingSelectedOnTap;
  _ChapterListView(
      {required this.chapterModel,
      required this.currentDubbing,
      required this.dubbingSelectedOnTap});

  @override
  State<_ChapterListView> createState() => _ChapterListViewPage();
}

class _ChapterListViewPage extends State<_ChapterListView> {
  @override
  Widget build(BuildContext context) {
    List<DubbingModel> dubbingList = widget.chapterModel.dubbingList ?? [];
    return Container(
      width: 1.sw,
      height: 350.r + MediaQuery.of(context).padding.bottom,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10), topRight: Radius.circular(10)),
      ),
      child: Stack(
        children: [
          Column(
            children: [
              Container(
                height: 50.r,
                child: Center(
                  child: Text(
                    widget.chapterModel.name ?? "",
                    style: TextStyle(
                        color: Colors.black,
                        fontSize: 15.sp,
                        fontWeight: FontWeight.w600),
                  ),
                ),
              ),
              Container(
                width: 1.sw,
                height: 300.r,
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  itemCount: dubbingList.length,
                  itemBuilder: (context, index) {
                    DubbingModel model = dubbingList[index];
                    return GestureDetector(
                      onTap: () => widget.dubbingSelectedOnTap(model),
                      child: _chapterListItem(model),
                    );
                  },
                ),
              ),
            ],
          ),
          Positioned(
            right: 0,
            top: 0,
            child: IconButton(
              onPressed: () {
                dismissDialog();
              },
              icon: Icon(
                Icons.close,
                color: Colors.grey,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _chapterListItem(DubbingModel model) {
    return GestureDetector(
      child: Container(
        width: 1.sw,
        height: 46.r,
        padding: EdgeInsets.only(left: 20.r, right: 20.r),
        decoration: BoxDecoration(color: Colors.white),
        child: Column(
          children: [
            Container(
              height: 44.r,
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(model.videoName ?? "",
                      style: TextStyle(
                          color: ThemeConfig.currentTheme.colorTextPrimary2,
                          fontSize: 15.sp)),
                  SizedBox(
                    width: 10.r,
                  ),
                  Visibility(
                    visible: widget.currentDubbing.id == model.id,
                    child: Image(
                      image: AssetImage(AssetsUtils.wrapAsset(
                          Assets.imagesTextDubbingPosition)),
                      height: 20.r,
                    ),
                  ),
                  SizedBox(
                    width: 10.r,
                  ),
                  Visibility(
                    visible: model.isState == "0",
                    child: Text(
                      context.string.textDubbingtj,
                      style: TextStyle(
                          color: ThemeConfig.currentTheme.colorTextPrimary1,
                          fontSize: 15.sp),
                    ),
                  ),
                  Expanded(child: Container()),
                  Visibility(
                    visible: model.isState == "0",
                    child: Text(
                      context.string.textDubbingdwc,
                      style: TextStyle(
                          color: ThemeConfig.currentTheme.colorSubRed,
                          fontSize: 15.sp),
                    ),
                  ),
                ],
              ),
            ),
            DottedLine(
              dashColor: ThemeConfig.currentTheme.colorDivider2,
            ),
          ],
        ),
      ),
    );
  }
}

class _CustomerserviceView extends StatelessWidget {
  final UnitDubbingModel chapterModel;
  final DubbingModel currentDubbing;
  final BookInfoDetail book;
  _CustomerserviceView(
      {required this.chapterModel,
      required this.currentDubbing,
      required this.book});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 1.sw,
      height: 280.r,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(10), topRight: Radius.circular(10)),
      ),
      child: Column(
        children: [
          _resolveFunctions(),
          SizedBox(
            height: 15.r,
          ),
          TextButton(
            onPressed: () {
              dismissDialog();
            },
            child: Text(
              context.string.textDubbingqx,
              style: TextStyle(
                  color: ThemeConfig.currentTheme.colorTextPrimary1,
                  fontSize: 16.sp),
            ),
          ),
        ],
      ),
    );
  }

  Widget _resolveFunctions() {
    return Padding(
      padding: EdgeInsets.only(top: 20.h),
      child: Wrap(
        runSpacing: 20,
        children: [
          ResolveItem(
            name: "网络不好",
            image: AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon1),
            onTap: () {
              dismissDialog();
              toPage(RouteName.networkBadGuid);
            },
          ),
          ResolveItem(
            name: "无法录音",
            image: AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon2),
            onTap: () {
              dismissDialog();
              toPage(RouteName.speechRecordIssueGuid);
            },
          ),
          ResolveItem(
            name: "录音得分低",
            image: AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon3),
            onTap: () {
              dismissDialog();
              toPage(RouteName.speechLowScoreGuid);
            },
          ),
          ResolveItem(
            name: "反馈",
            image: AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon4),
            onTap: () {
              dismissDialog();

              toPage(RouteName.feedback,
                  extra: FeedbackPageParam(
                      moduleName: "课文配音",
                      unitAndPart:
                          "${chapterModel.name ?? ""}${currentDubbing.videoName ?? ""}",
                      type: "1",
                      unitId: chapterModel.id ?? "",
                      moduleId: "47",
                      bookInfoItem:
                          SimpleBookInfoModel.fromBookInfoDetail(book)));
            },
          ),
          ResolveItem(
            name: "客服中心",
            image: AssetsUtils.wrapAsset(Assets.imagesNewspeechSettingIcon5),
            onTap: () {
              dismissDialog();
              toPage(RouteName.helpAndSupport);
            },
          ),
        ],
      ),
    );
  }
}

class ResolveItem extends StatelessWidget {
  final String name;
  final String image;
  final VoidCallback? onTap;
  const ResolveItem(
      {super.key, required this.name, required this.image, this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: (1.sw - 30.w) / 3,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              image,
              width: 50.r,
            ),
            Padding(
              padding: const EdgeInsets.only(top: 8.0),
              child: Text(
                name,
                style: ThemeConfig.currentTheme.text15,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class _RianglePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..color = Color(0xfffe6d50); // 设置三角形的颜色

    final path = Path();
    path.moveTo(-0.2, 0); // 三角形顶部顶点
    path.lineTo(size.width * 0.5, size.height * 0.5);
    path.lineTo(-0.2, size.height);
    path.close();

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }
}

class _LineProgressIndicator extends StatefulWidget {
  final int totalSecond;
  final double progress;
  _LineProgressIndicator({required this.totalSecond, required this.progress});
  @override
  State<_LineProgressIndicator> createState() => _LineProgressIndicatorPage();
}

class _LineProgressIndicatorPage extends State<_LineProgressIndicator> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        Row(
          children: [
            Container(
              width: 230.r,
              height: 6.r,
              decoration: BoxDecoration(
                color: ThemeConfig.currentTheme.colorDivider2,
                borderRadius: BorderRadius.all(Radius.circular(3.r)),
              ),
            ),
            SizedBox(
              width: 5.r,
            ),
            Text(
              "${widget.totalSecond}s",
              style: TextStyle(
                  color: ThemeConfig.currentTheme.colorTextPrimary1,
                  fontSize: 14.sp),
            ),
          ],
        ),
        Positioned(
          left: 0,
          child: Container(
            width: 230.r * widget.progress,
            height: 6.r,
            decoration: BoxDecoration(
              color: ThemeConfig.currentTheme.colorBgOrange,
              borderRadius: BorderRadius.all(Radius.circular(3.r)),
            ),
          ),
        ),
      ],
    );
  }
}

class ScoreRow extends ConsumerStatefulWidget {
  final TextDubbingDubbingProvider controller;
  final int index;
  const ScoreRow({super.key, required this.controller, required this.index});

  @override
  ConsumerState<ScoreRow> createState() => _ScoreRowState();
}

class _ScoreRowState extends ConsumerState<ScoreRow> {
  @override
  void initState() {
    super.initState();

    widget.controller.singsoundResultUpdateIndexNotifier.addListener(() {
      Logger.info(
          "11singsoundResultUpdateIndexNotifier===${widget.controller.singsoundResultUpdateIndexNotifier.value},,,${widget.index}");
      if (widget.controller.activeIndexNotifier.value == widget.index) {
        if (mounted) {
          setState(() {});
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    SingsoundResult? result = widget.controller
        .dubbingResultMap[widget.controller.currentDubbing.value.id ?? ""];
    double activeStar = DubbingUtil.getActiveStar(result?.overall ?? 0);
    Logger.info("singsoundResultUpdateIndexNotifier===${activeStar}");
    return result == null
        ? const SizedBox.shrink()
        : RatingView(activeStar: activeStar, total: 3);
  }
}
