import { Any } from '@ohos/flutter_ohos';
export default class AndroidResource {
    private name;
    private defType;
    private defPackage;
    constructor(name: string, defType: string | null, defPackage: string | null);
    static fromMap(map: Map<string, Any>): AndroidResource | null;
    toMap(): Map<string, Any>;
    getName(): string;
    setName(name: string): void;
    getDefType(): String;
    setDefType(defType: null): void;
    getDefPackage(): string;
    setDefPackage(defPackage: string): void;
    getIdentifier(ctx: Context): number;
    equals(o: Any): boolean;
    hashCode(): number;
    toString(): string;
}
