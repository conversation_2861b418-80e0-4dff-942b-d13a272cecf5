import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import PullToRefreshLayout from './PullToRefreshLayout';
export default class PullToRefreshChannelDelegate extends ChannelDelegateImpl {
    private pullToRefreshView;
    constructor(pullToRefreshView: PullToRefreshLayout, channel: MethodChannel);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    onRefresh(): void;
    dispose(): void;
}
