{"modelVersion": "5.0.0", "name": "yyb_flutter", "version": "1.0.0", "description": "Please describe the basic information.", "main": "", "author": "", "license": "", "dependencies": {"@ohos/flutter_ohos": "file:./har/flutter.har", "@ohos/mp4parser": "2.0.3-rc.1", "@umeng/common": "^1.1.3", "@umeng/analytics": "^1.2.4"}, "devDependencies": {"@ohos/hypium": "1.0.6"}, "overrides": {"@ohos/flutter_ohos": "file:./har/flutter.har", "shared_preferences_ohos": "file:./har/shared_preferences_ohos.har", "permission_handler_ohos": "file:./har/permission_handler_ohos.har", "@ohos/flutter_module": "file:./entry", "path_provider_ohos": "file:./har/path_provider_ohos.har", "package_info_plus": "file:./har/package_info_plus.har", "flutter_inappwebview_ohos": "file:./har/flutter_inappwebview_ohos.har", "url_launcher_ohos": "file:./har/url_launcher_ohos.har", "video_player_ohos": "file:./har/video_player_ohos.har", "flutter_sound": "file:./har/flutter_sound.har", "audioplayers_ohos": "file:./har/audioplayers_ohos.har", "image_picker_ohos": "file:./har/image_picker_ohos.har", "connectivity_plus": "file:./har/connectivity_plus.har", "device_info_plus": "file:./har/device_info_plus.har", "fluwx": "file:./har/fluwx.har", "mobile_scanner": "file:./har/mobile_scanner.har", "audio_session": "file:./har/audio_session.har", "just_audio_ohos": "file:./har/just_audio_ohos.har", "screen_brightness_ohos": "file:./har/screen_brightness_ohos.har"}, "dynamicDependencies": {}}