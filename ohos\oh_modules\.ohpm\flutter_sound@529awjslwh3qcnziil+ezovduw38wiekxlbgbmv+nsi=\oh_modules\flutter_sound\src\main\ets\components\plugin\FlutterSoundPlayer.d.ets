/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import { FlutterSoundSession } from './FlutterSoundSession';
import { t_PLAYER_STATE } from './FlutterSoundTypes';
import common from "@ohos.app.ability.common";
import { FlutterSoundPlayerCallback } from './FlutterSoundPlayerCallback';
export declare class FlutterSoundPlayer extends FlutterSoundSession implements FlutterSoundPlayerCallback {
    static ERR_UNKNOWN: string;
    static ERR_PLAYER_IS_NULL: string;
    static ERR_PLAYER_IS_PLAYING: string;
    static context: common.UIAbilityContext | undefined;
    private mPlayer;
    private audioFile;
    openPlayerCompleted(success: boolean): void;
    closePlayerCompleted(success: boolean): void;
    stopPlayerCompleted(success: boolean): void;
    pausePlayerCompleted(success: boolean): void;
    resumePlayerCompleted(success: boolean): void;
    startPlayerCompleted(success: boolean, duration: number): void;
    needSomeFood(ln: number): void;
    updateProgress(position: number, duration: number): void;
    audioPlayerDidFinishPlaying(flag: boolean): void;
    updatePlaybackState(newState: t_PLAYER_STATE): void;
    constructor(call: MethodCall);
    getPlugin(): FlutterSoundManager;
    getStatus(): number;
    getPlayerState(): t_PLAYER_STATE;
    openPlayer(call: MethodCall, result: MethodResult): Promise<void>;
    closePlayer(call: MethodCall, result: MethodResult): Promise<void>;
    reset(call: MethodCall, result: MethodResult): Promise<void>;
    startPlayerFromMic(call: MethodCall, result: MethodResult): void;
    startPlayer(call: MethodCall, result: MethodResult): Promise<void>;
    feed(call: MethodCall, result: MethodResult): void;
    stopPlayer(call: MethodCall, result: MethodResult): Promise<void>;
    isDecoderSupported(call: MethodCall, result: MethodResult): void;
    pausePlayer(call: MethodCall, result: MethodResult): Promise<void>;
    resumePlayer(call: MethodCall, result: MethodResult): Promise<void>;
    seekToPlayer(call: MethodCall, result: MethodResult): Promise<void>;
    setVolume(call: MethodCall, result: MethodResult): void;
    setSpeed(call: MethodCall, result: MethodResult): void;
    setSubscriptionDuration(call: MethodCall, result: MethodResult): void;
    getProgress(call: MethodCall, result: MethodResult): void;
    getResourcePath(call: MethodCall, result: MethodResult): void;
    getPlayerStateByChannel(call: MethodCall, result: MethodResult): void;
    setLogLevel(call: MethodCall, result: MethodResult): void;
}
