import common from '@ohos.app.ability.common';
import UIAbility from '@ohos.app.ability.UIAbility';
import ArrayList from '@ohos.util.ArrayList';
import { ErrorCallback } from './ErrorCallback';
export declare class PermissionManager {
    private tokenId;
    private atManager;
    private ongoing;
    private ability;
    private successCallback;
    private requestResults;
    constructor();
    /**
     * 检测权限状态
     *
     * @param permission 权限
     * @param context 上下文
     * @param successCallback 回调结果
     */
    checkPermissionStatus(permission: number, context: common.Context, successCallback: CheckPermissionSuccessCallback): void;
    private determinePermissionStatus;
    requestPermissions(permissions: ArrayList<number>, ability: UIAbility, successCallback: RequestPermissionsSuccessCallback, errorCallback: ErrorCallback): void;
    private getPermissionToRequest;
    private checkBluetoothPermissionStatus;
    private checkPermission;
    shouldShowRequestPermissionRationale(permission: number, callback: ShouldShowRequestPermissionRationaleSuccessCallback, errorCallback: ErrorCallback): void;
}
export interface CheckPermissionSuccessCallback {
    onSuccess(permissionStatus: number): void;
}
export interface RequestPermissionsSuccessCallback {
    onSuccess(results: Map<number, number>): void;
}
export interface ShouldShowRequestPermissionRationaleSuccessCallback {
    onSuccess(shouldShowRequestPermissionRationale: boolean): void;
}
