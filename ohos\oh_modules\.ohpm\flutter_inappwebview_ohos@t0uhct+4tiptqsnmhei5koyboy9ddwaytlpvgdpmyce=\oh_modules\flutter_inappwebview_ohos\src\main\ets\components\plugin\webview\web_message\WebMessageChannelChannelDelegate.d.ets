import { MethodCall, MethodChannel } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import ChannelDelegateImpl from '../../types/ChannelDelegateImpl';
import WebMessageCompatExt from '../../types/WebMessageCompatExt';
import { WebMessageChannel } from './WebMessageChannel';
export declare class WebMessageChannelChannelDelegate extends ChannelDelegateImpl {
    private webMessageChannel;
    constructor(webMessageChannel: WebMessageChannel, channel: MethodChannel);
    onMethodCall(call: MethodCall, result: MethodResult): void;
    onMessage(index: number, message: WebMessageCompatExt | null): void;
    dispose(): void;
}
