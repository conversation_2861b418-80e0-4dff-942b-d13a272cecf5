import { Any } from '@ohos/flutter_ohos';
export default class ClientCertResponse {
    private certificatePath;
    private certificatePassword;
    private keyStoreType;
    private action;
    constructor(certificatePath: string, certificatePassword: string, keyStoreType: string, action: number);
    static fromMap(map: Map<string, Any>): ClientCertResponse | null;
    getCertificatePath(): string;
    setCertificatePath(certificatePath: string): void;
    getCertificatePassword(): string;
    setCertificatePassword(certificatePassword: string): void;
    getKeyStoreType(): string;
    setKeyStoreType(keyStoreType: string): void;
    getAction(): number;
    setAction(action: number): void;
}
