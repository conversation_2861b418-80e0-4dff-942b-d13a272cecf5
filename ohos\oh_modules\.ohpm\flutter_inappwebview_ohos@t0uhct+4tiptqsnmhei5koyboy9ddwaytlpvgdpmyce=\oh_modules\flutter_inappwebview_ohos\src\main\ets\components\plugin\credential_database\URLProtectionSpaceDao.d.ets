import URLProtectionSpace from '../types/URLProtectionSpace';
import CredentialDatabaseHelper from './CredentialDatabaseHelper';
export default class URLProtectionSpaceDao {
    credentialDatabaseHelper: CredentialDatabaseHelper;
    projection: Array<string>;
    constructor(credentialDatabaseHelper: CredentialDatabaseHelper);
    getAll(): Promise<URLProtectionSpace[]>;
    find(host: string, protocol: string, realm: string, port: number): Promise<URLProtectionSpace>;
    insert(URLProtectionSpace: URLProtectionSpace): Promise<number>;
    delete(URLProtectionSpace: URLProtectionSpace): Promise<number>;
}
