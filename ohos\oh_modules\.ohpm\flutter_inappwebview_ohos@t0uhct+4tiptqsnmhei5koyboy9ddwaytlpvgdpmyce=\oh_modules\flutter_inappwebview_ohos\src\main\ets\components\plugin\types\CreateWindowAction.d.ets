import { Any } from '@ohos/flutter_ohos';
import NavigationAction from './NavigationAction';
import URLRequest from './URLRequest';
export default class CreateWindowAction extends NavigationAction {
    windowId: number;
    isDialog: boolean;
    constructor(request: URLRequest, isForMainFrame: boolean, hasGesture: boolean, isRedirect: boolean, windowId: number, isDialog: boolean);
    toMap(): Map<string, Any>;
    getWindowId(): number;
    setWindowId(windowId: number): void;
    getDialog(): boolean;
    setDialog(dialog: boolean): void;
    toString(): string;
}
