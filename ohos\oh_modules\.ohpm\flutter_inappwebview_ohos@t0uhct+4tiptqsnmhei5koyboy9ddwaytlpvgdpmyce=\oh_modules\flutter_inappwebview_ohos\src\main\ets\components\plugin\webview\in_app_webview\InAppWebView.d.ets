import web_webview from '@ohos.web.webview';
import { Params } from '@ohos/flutter_ohos/src/main/ets/plugin/platform/PlatformView';
import URLRequest from '../../types/URLRequest';
import InAppWebViewFlutterPlugin from '../../InAppWebViewFlutterPlugin';
import InAppWebViewInterface from '../InAppWebViewInterface';
import InAppWebViewSettings from './InAppWebViewSettings';
import { Any } from '@ohos/flutter_ohos';
import UserScript from '../../types/UserScript';
import WebViewChannelDelegate from '../WebViewChannelDelegate';
import ContentWorld from '../../types/ContentWorld';
import { ValueCallback } from '../../types/ValueCallback';
import { WebMessageChannel } from '../web_message/WebMessageChannel';
import UserContentController from '../../types/UserContentController';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import InAppWebViewClient from './InAppWebViewClient';
import PluginScript from '../../types/PluginScript';
import { InAppBrowserDelegate } from '../../in_app_browser/InAppBrowserDelegate';
import HashMap from "@ohos.util.HashMap";
import PrintJobSettings from '../../print_job/PrintJobSettings';
import WebMessageListener from '../web_message/WebMessageListener';
import WebViewAssetLoaderExt from '../../types/WebViewAssetLoaderExt';
import ContentBlockerHandler from '../../content_blocker/ContentBlockerHandler';
import cert from '@ohos.security.cert';
import { FindInteractionController } from '../../find_interaction/FindInteractionController';
import PullToRefreshLayout from '../../pull_to_refresh/PullToRefreshLayout';
export default class InAppWebView implements InAppWebViewInterface {
    plugin: InAppWebViewFlutterPlugin;
    inAppBrowserDelegate: InAppBrowserDelegate | null;
    id: number;
    windowId: number | null | undefined;
    inAppWebViewClient: InAppWebViewClient | null;
    channelDelegate: WebViewChannelDelegate | null;
    private javaScriptBridgeInterface;
    customSettings: InAppWebViewSettings;
    isLoading: boolean;
    private inFullscreen;
    zoomScale: number;
    contentBlockerHandler: ContentBlockerHandler;
    regexToCancelSubFramesLoadingCompiled: RegExp | null;
    contextMenu: Map<string, Any> | undefined;
    initialPositionScrollStoppedTask: number;
    newCheckScrollStoppedTask: number;
    newCheckContextMenuShouldBeClosedTaskTask: number;
    userContentController: UserContentController;
    callAsyncJavaScriptCallbacks: Map<string, ValueCallback<string>>;
    evaluateJavaScriptContentWorldCallbacks: Map<string, ValueCallback<string>>;
    webMessageChannels: Map<string, WebMessageChannel>;
    webMessageListeners: Array<WebMessageListener>;
    private initialUserOnlyScripts;
    findInteractionController: FindInteractionController | null;
    webViewAssetLoaderExt: WebViewAssetLoaderExt | null;
    private interceptOnlyAsyncAjaxRequestsPluginScript;
    context: Context;
    controller: web_webview.WebviewController;
    private ohosWebViewModel;
    private controllerAttached;
    private pullToRefreshLayout;
    private viewWidth;
    private viewHeight;
    private curUrl;
    constructor(context: Context, plugin: InAppWebViewFlutterPlugin, id: number, windowId: number | null | undefined, customSettings: InAppWebViewSettings, userScripts: Array<UserScript>, contextMenu?: Map<string, Any>);
    setPullToRefreshLayout(pullToRefreshLayout: PullToRefreshLayout): void;
    getPullToRefreshLayout(): PullToRefreshLayout;
    prepare(): Promise<void>;
    prepareAndAddUserScripts(): void;
    setIncognito(enabled: boolean): void;
    setCacheEnabled(enabled: boolean): void;
    loadUrl(urlRequest: URLRequest): Promise<void>;
    loadFile(assetFilePath: string): Promise<void>;
    getLoadUrl(): string | Resource;
    getLoading(): boolean;
    clearCookies(): void;
    refresh(): void;
    scrollToTop(): void;
    clearAllCache(): void;
    takeScreenshot(screenshotConfiguration: Map<string, Any> | null, result: MethodResult): Promise<void>;
    setSettings(newCustomSettings: InAppWebViewSettings, newSettingsMap: Map<string, Any>): void;
    getCustomSettings(): Map<string, Any> | null;
    enablePluginScriptAtRuntime(flagVariable: string, enable: boolean, pluginScript: PluginScript): void;
    injectDeferredObject(source: string, contentWorld: ContentWorld | null, jsWrapper: string | null, resultCallback: ValueCallback<string> | null): void;
    evaluateJavascript(source: string, contentWorld: ContentWorld | null, resultCallback: ValueCallback<string> | null): void;
    injectJavascriptFileFromUrl(urlFile: string, scriptHtmlTagAttributes: Map<string, Any> | null): void;
    injectCSSCode(source: string): void;
    injectCSSFileFromUrl(urlFile: string, cssLinkHtmlTagAttributes: Map<string, Any> | null): void;
    getCopyBackForwardList(): HashMap<string, Any>;
    scrollTo(x: number, y: number, animated: boolean): void;
    scrollBy(x: number, y: number, animated: boolean): Promise<void>;
    printCurrentPage(settings: PrintJobSettings): string | null;
    dispose(): void;
    getUrl(): string;
    getTitle(): string;
    getProgress(): number;
    reload(): void;
    canGoBack(): boolean;
    goBack(): void;
    canGoForward(): boolean;
    goForward(): void;
    canGoBackOrForward(steps: number): boolean;
    goBackOrForward(steps: number): void;
    stopLoading(): void;
    clearSslPreferences(): void;
    findAllAsync(find: string): void;
    findNext(forward: boolean): void;
    clearMatches(): void;
    onPause(): void;
    onResume(): void;
    pauseTimers(): void;
    resumeTimers(): void;
    getContentHeight(): number;
    getContentWidth(callback: ValueCallback<number>): void;
    saveWebArchive(baseName: string, autoName: boolean, callback: ValueCallback<string>): void;
    isSecureContext(resultCallback: ValueCallback<boolean>): void;
    zoomBy(zoomFactor: number): void;
    getOriginalUrl(): string;
    getZoomScale(): number;
    getHitTestResult(): web_webview.HitTestValue;
    pageDown(bottom: boolean): boolean;
    pageUp(top: boolean): boolean;
    zoomIn(): boolean;
    zoomOut(): boolean;
    clearFocus(): void;
    getCertificate(): Promise<cert.X509Cert>;
    clearHistory(): void;
    postUrl(url: string, postData: ArrayBuffer | null): Promise<void>;
    loadDataWithBaseURL(baseUrl: string, data: string, mimeType: string, encoding: string, historyUrl: string): Promise<void>;
    getController(): web_webview.WebviewController;
    getInAppBrowserDelegate(): InAppBrowserDelegate | null;
    setContextMenu(contextMenu: Map<string, Any>): void;
    requestFocusNodeHref(): Map<string, Any>;
    requestImageRef(): Map<string, Any>;
    createCompatWebMessageChannel(): WebMessageChannel;
    addWebMessageListener(webMessageListener: WebMessageListener): void;
    canScrollVertically(): boolean;
    canScrollHorizontally(callback: ValueCallback<boolean>): void;
    isInFullscreen(): boolean;
    setInFullscreen(inFullscreen: boolean): void;
    getPlugin(): InAppWebViewFlutterPlugin;
    getWebMessageChannels(): Map<string, WebMessageChannel>;
    getUserContentController(): UserContentController;
    onControllerAttached: () => void;
    onAreaChange: (oldValue: Area, newValue: Area) => void;
    toWebHeaders(headers: Map<string, string>): Array<web_webview.WebHeader>;
    private waitControllerAttached;
    private checkControllerAttached;
    isDebuggingEnabled(): boolean;
    disposeWebMessageChannels(): void;
    hideContextMenu(): void;
    getSelectedText(resultCallback: ValueCallback<string>): void;
    callAsyncJavaScript(functionBody: string, args: Map<string, Any>, contentWorld: ContentWorld | null, resultCallback: ValueCallback<string>): void;
    getView(): WrappedBuilder<[Params]>;
    private onSettinsUpdated;
}
