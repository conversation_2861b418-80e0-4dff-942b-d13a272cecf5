import { MethodChannel } from '@ohos/flutter_ohos';
import ChannelDelegateImpl from '../types/ChannelDelegateImpl';
import InAppBrowserMenuItem from '../types/InAppBrowserMenuItem';
export default class InAppBrowserChannelDelegate extends ChannelDelegateImpl {
    constructor(channel: MethodChannel);
    onBrowserCreated(): void;
    onMenuItemClicked(menuItem: InAppBrowserMenuItem): void;
    onExit(): void;
}
