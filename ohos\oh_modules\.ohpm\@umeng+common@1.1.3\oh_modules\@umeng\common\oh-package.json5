{"license": "Apache-2.0", "devDependencies": {}, "keywords": ["友盟", "友盟统计"], "author": "友盟", "name": "@umeng/common", "description": "适配原生鸿蒙的友盟统计分析sdk的common包模块，用于日志的缓存和发送", "repository": "https://devs.umeng.com/?platform=harmony", "version": "1.1.3", "homepage": "https://www.umeng.com", "dependencies": {"libcommon.so": "./src/main/cpp/types/libcommon"}, "types": "./Index.d.ts", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "dependencyPkgVersion": {"@umeng/common": "1.1.2"}, "declarationEntry": [], "nativeDebugSymbol": true, "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true, "nativeComponents": [{"name": "libcommon.so", "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "linkLibraries": []}]}