import ContentBlocker from './ContentBlocker';
import ArrayList from "@ohos.util.ArrayList";
import InAppWebView from '../webview/in_app_webview/InAppWebView';
import WebResourceRequestExt from '../types/WebResourceRequestExt';
import { ContentBlockerTriggerResourceType } from './ContentBlockerTriggerResourceType';
export default class ContentBlockerHandler {
    protected ruleList: ArrayList<ContentBlocker>;
    constructor(ruleList?: ArrayList<ContentBlocker>);
    getRuleList(): ArrayList<ContentBlocker>;
    setRuleList(newRuleList: ArrayList<ContentBlocker>): void;
    checkUrl(webView: InAppWebView, request: WebResourceRequestExt, contentType?: string): WebResourceResponse | null;
    checkUrlWithResourceType(webView: InAppWebView, request: WebResourceRequestExt, responseResourceType: ContentBlockerTriggerResourceType): WebResourceResponse | null;
    getResourceTypeFromContentType(contentType: string): ContentBlockerTriggerResourceType;
}
