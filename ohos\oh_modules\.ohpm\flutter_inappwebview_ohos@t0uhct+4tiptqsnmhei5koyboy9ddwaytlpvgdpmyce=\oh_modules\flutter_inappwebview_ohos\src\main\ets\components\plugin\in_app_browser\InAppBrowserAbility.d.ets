import UIAbility from '@ohos.app.ability.UIAbility';
import window from '@ohos.window';
import Want from '@ohos.app.ability.Want';
import AbilityConstant from '@ohos.app.ability.AbilityConstant';
import PullToRefreshLayout from '../pull_to_refresh/PullToRefreshLayout';
import { Disposable } from '../types/Disposable';
import InAppWebView from '../webview/in_app_webview/InAppWebView';
import { InAppBrowserDelegate } from './InAppBrowserDelegate';
import InAppBrowserSettings from './InAppBrowserSettings';
import List from "@ohos.util.List";
import { ActivityResultListener } from './ActivityResultListener';
import InAppBrowserManager from './InAppBrowserManager';
import InAppBrowserChannelDelegate from './InAppBrowserChannelDelegate';
import InAppBrowserMenuItem from '../types/InAppBrowserMenuItem';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { Any } from '@ohos/flutter_ohos';
export default class InAppBrowserAbility extends UIAbility implements InAppBrowserDelegate, Disposable {
    windowId: string | null;
    id: string | null;
    webView: InAppWebView | null;
    pullToRefreshLayout: PullToRefreshLayout | null;
    customSettings: InAppBrowserSettings;
    isHidden: boolean;
    fromActivity: string | null;
    private activityResultListeners;
    manager: InAppBrowserManager | null;
    channelDelegate: InAppBrowserChannelDelegate | null;
    menuItems: List<InAppBrowserMenuItem>;
    onCreate(want: Want, launchParam: AbilityConstant.LaunchParam): void;
    onWindowStageCreate(windowStage: window.WindowStage): void;
    private prepareView;
    getCustomSettings(): Map<string, Any> | null;
    setSettings(newSettings: InAppBrowserSettings, newSettingsMap: Map<string, Any>): void;
    hide(): void;
    show(): void;
    close(result: MethodResult): void;
    getActivityResultListeners(): List<ActivityResultListener>;
    didChangeTitle(title: string): void;
    didStartNavigation(url: string): void;
    didUpdateVisitedHistory(url: string): void;
    didFinishNavigation(url: string): void;
    didFailNavigation(url: string, errorCode: number, description: string): void;
    didChangeProgress(progress: number): void;
    dispose(): void;
}
