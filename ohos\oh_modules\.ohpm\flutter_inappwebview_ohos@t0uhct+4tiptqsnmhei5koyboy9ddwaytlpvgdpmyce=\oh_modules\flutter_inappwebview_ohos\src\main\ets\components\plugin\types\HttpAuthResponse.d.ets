import { Any } from '@ohos/flutter_ohos';
export default class HttpAuthResponse {
    private username;
    private password;
    permanentPersistence: boolean;
    private action;
    constructor(username: string, password: string, permanentPersistence: boolean, action: number | null);
    static fromMap(map: Map<string, Any>): HttpAuthResponse | null;
    getUsername(): string;
    setUsername(username: string): void;
    getPassword(): string;
    setPassword(password: string): void;
    isPermanentPersistence(): boolean;
    setPermanentPersistence(permanentPersistence: boolean): void;
    getAction(): number | null;
    setAction(action: number | null): void;
    toString(): string;
}
