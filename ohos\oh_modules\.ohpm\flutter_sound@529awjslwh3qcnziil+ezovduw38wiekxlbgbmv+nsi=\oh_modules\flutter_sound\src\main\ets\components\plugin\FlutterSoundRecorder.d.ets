/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import { FlutterSoundManager } from './FlutterSoundManager';
import { FlutterSoundSession } from './FlutterSoundSession';
import common from "@ohos.app.ability.common";
import { FlutterSoundRecorderCallback } from './FlutterSoundRecorderCallback';
export declare class FlutterSoundRecorder extends FlutterSoundSession implements FlutterSoundRecorderCallback {
    static ERR_UNKNOWN: string;
    static ERR_RECORDER_IS_NULL: string;
    static ERR_RECORDER_IS_RECORDING: string;
    static context: common.UIAbilityContext | undefined;
    private mRecorder;
    openRecorderCompleted(success: boolean): void;
    closeRecorderCompleted(success: boolean): void;
    stopRecorderCompleted(success: boolean, url: string): void;
    pauseRecorderCompleted(success: boolean): void;
    resumeRecorderCompleted(success: boolean): void;
    startRecorderCompleted(success: boolean): void;
    updateRecorderProgressDbPeakLevel(dbPeakLevel: number, duration: number): void;
    recordingData(data: ArrayBuffer): void;
    constructor(call: MethodCall);
    getPlugin(): FlutterSoundManager;
    getStatus(): number;
    reset(call: MethodCall, result: MethodResult): Promise<void>;
    openRecorder(call: MethodCall, result: MethodResult): Promise<void>;
    closeRecorder(call: MethodCall, result: MethodResult): Promise<void>;
    isEncoderSupported(call: MethodCall, result: MethodResult): void;
    invokeMethodString(methodName: string, arg: string): void;
    startRecorder(call: MethodCall, result: MethodResult): Promise<void>;
    stopRecorder(call: MethodCall, result: MethodResult): Promise<void>;
    pauseRecorder(call: MethodCall, result: MethodResult): Promise<void>;
    resumeRecorder(call: MethodCall, result: MethodResult): Promise<void>;
    setSubscriptionDuration(call: MethodCall, result: MethodResult): void;
    getRecordURL(call: MethodCall, result: MethodResult): void;
    deleteRecord(call: MethodCall, result: MethodResult): Promise<void>;
    setLogLevel(call: MethodCall, result: MethodResult): void;
}
