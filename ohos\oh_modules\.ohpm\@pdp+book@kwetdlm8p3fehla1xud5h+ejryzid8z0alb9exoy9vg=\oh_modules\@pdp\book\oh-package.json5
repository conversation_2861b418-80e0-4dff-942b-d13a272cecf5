{"name": "@pdp/book", "version": "1.0.0", "description": "课本点读引擎", "author": "进馨网络科技有限公司", "license": "Apache-2.0", "dependencies": {"dayjs": "^1.11.7", "@ohos/lottie": "^2.0.15", "class-transformer": "^0.5.1", "reflect-metadata": "^0.1.13", "@ohos/crypto-js": "^2.0.4", "@pdp/swiper": "^1.0.0", "@pdp/evaluation": "^1.1.1", "bigdata": "file:src/lib/bigdata.har"}, "types": "Index.d.ets", "artifactType": "obfuscation", "metadata": {"byteCodeHar": true, "sourceRoots": ["./src/main"], "debug": false, "dependencyPkgVersion": {"dayjs": "1.11.13", "@ohos/lottie": "2.0.15", "class-transformer": "0.5.1", "reflect-metadata": "0.1.13", "@ohos/crypto-js": "2.0.4", "@pdp/swiper": "1.0.0", "@pdp/evaluation": "1.1.1", "bigdata": "1.0.0"}, "declarationEntry": [], "useNormalizedOHMUrl": true}, "compatibleSdkVersion": 12, "compatibleSdkType": "HarmonyOS", "obfuscated": true}