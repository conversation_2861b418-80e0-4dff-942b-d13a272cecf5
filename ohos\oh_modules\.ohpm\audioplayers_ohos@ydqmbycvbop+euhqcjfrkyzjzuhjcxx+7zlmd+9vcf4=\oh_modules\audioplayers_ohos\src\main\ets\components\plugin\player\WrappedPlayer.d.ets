/**
 * Copyright (c) 2024 Hunan OpenValley Digital Industry Development Co., Ltd.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import AudioContextOhos from '../AudioContextOhos';
import AudioplayersPlugin, { EventHandler } from '../AudioplayersPlugin';
import { ReleaseMode } from '../ReleaseMode';
import { PlayerMode } from '../PlayerMode';
import Source from '../source/Source';
import { Context as Context } from "@ohos.abilityAccessCtrl";
import audio from '@ohos.multimedia.audio';
import { SoundPoolManager } from './SoundPoolPlayer';
export default class WrappedPlayer {
    private ref;
    eventHandler: EventHandler;
    context: AudioContextOhos;
    private soundPoolManager;
    private player;
    private source;
    private released;
    private prepared;
    private playing;
    private volume;
    private balance;
    private rate;
    private releaseMode;
    private isLooping;
    private playerMode;
    private shouldSeekTo;
    private focusManager;
    constructor(plugin: AudioplayersPlugin, eventHandler: EventHandler, context: AudioContextOhos, soundPoolManager: SoundPoolManager);
    setSource(value: Source | null): Promise<void>;
    getSource(): Source | null;
    isPlaying(): boolean;
    setVolume(value: number): void;
    setBalance(value: number): void;
    setRate(value: number): void;
    setReleaseMode(value: ReleaseMode): void;
    getLooping(): boolean;
    setPlayerMode(value: PlayerMode): Promise<void>;
    setPrepared(value: boolean): void;
    getPrepared(): boolean;
    getVolume(): number;
    getRate(): number;
    private maybeGetCurrentPosition;
    private getOrCreatePlayer;
    updateAudioContext(audioContext: AudioContextOhos): Promise<void>;
    getDuration(): number | null;
    getCurrentPosition(): number | null;
    isActuallyPlaying(): boolean;
    getApplicationContext(): Context;
    getAudioManager(): audio.AudioManager;
    play(): void;
    actuallyPlay(): Promise<void>;
    stop(): Promise<void>;
    release(): Promise<void>;
    pause(): Promise<void>;
    seek(position: number): void;
    /**
     * Player callbacks
     */
    onPrepared(): Promise<void>;
    onCompletion(): Promise<void>;
    onBuffering(percent: number): void;
    onSeekComplete(): void;
    handleLog(message: string): void;
    handleError(errorCode?: string, errorMessage?: string, errorDetails?: ESObject): void;
    onError(what: number, extra: string): Boolean;
    /**
     * Create new player
     */
    private createPlayer;
    /**
     * Create new player, assign and configure source
     */
    private initPlayer;
    private configAndPrepare;
    private setVolumeAndBalance;
    dispose(): Promise<void>;
    startContinuousTask(): void;
    stopContinuousTask(): void;
}
