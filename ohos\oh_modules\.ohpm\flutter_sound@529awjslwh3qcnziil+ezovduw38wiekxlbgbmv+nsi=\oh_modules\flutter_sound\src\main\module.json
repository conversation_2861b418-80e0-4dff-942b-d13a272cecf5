{"app": {"bundleName": "com.yyb.hm", "debug": true, "versionCode": 827, "versionName": "8.2.7", "minAPIVersion": 50000012, "targetAPIVersion": 50005017, "apiReleaseType": "Release", "compileSdkVersion": "5.0.5.165", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "flutter_sound", "type": "har", "deviceTypes": ["default", "tablet"], "packageName": "flutter_sound", "installationFree": false, "virtualMachine": "ark12.0.2.0", "compileMode": "esmodule", "dependencies": []}}