[{"Name": "mp4parser", "License": "Apache License 2.0", "License File": " LICENSE ", "Version Number": "*******", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/sannies/mp4parser", "Description": "A Java API to read, write and create MP4 files"}, {"Name": "ffmpeg", "License": "Most files in FFmpeg are under the GNU Lesser General Public License version 2.1 or later (LGPL v2.1+). Read the file COPYING.LGPLv2.1 for details. Some other files have MIT/X11/BSD-style licenses. In combination the LGPL v2.1+ applies to FFmpeg. Some optional parts of FFmpeg are licensed under the GNU General Public License version 2 or later (GPL v2+). See the file COPYING.GPLv2 for details. None of these parts are used by default, you have to explicitly pass --enable-gpl to configure to activate them. In this case, FFmpeg's license changes to GPL v2+.", "License File": "LICENSE.md", "Version Number": "n4.2.5", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/FFmpeg/FFmpeg", "Description": "FFmpeg is a collection of libraries and tools to process multimedia content such as audio, video, subtitles and related metadata."}, {"Name": "FFmpeg-Invoker", "License": "Apache License 2.0", "License File": "README.md", "Version Number": "master", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/xch168/FFmpeg-Invoker", "Description": "FFmpeg invoker for Android, base on FFmpeg 4.2.(Supports Android 4.1+)"}, {"Name": "FFmpegMediaMetadataRetriever", "License": "Apache License 2.0", "License File": "LICENSE-2.0.txt", "Version Number": "1.0.19", "Owner": "<EMAIL>", "Upstream URL": "https://github.com/wseemann/FFmpegMediaMetadataRetriever", "Description": "FFmpegMediaMetadataRetriever provides a unified interface for retrieving frame and meta data from an input media file."}]