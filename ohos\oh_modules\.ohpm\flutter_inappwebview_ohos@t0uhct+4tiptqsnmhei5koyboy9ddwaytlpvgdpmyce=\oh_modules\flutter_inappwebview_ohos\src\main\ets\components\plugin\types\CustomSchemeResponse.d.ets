import { Any } from '@ohos/flutter_ohos';
export default class CustomSchemeResponse {
    private data;
    private contentType;
    private contentEncoding;
    constructor(data: ArrayBuffer, contentType: string, contentEncoding: string);
    static fromMap(map: Map<string, Any>): CustomSchemeResponse | null;
    getData(): ArrayBuffer;
    setData(data: ArrayBuffer): void;
    getContentType(): string;
    setContentType(contentType: string): void;
    getContentEncoding(): string;
    setContentEncoding(contentEncoding: string): void;
}
