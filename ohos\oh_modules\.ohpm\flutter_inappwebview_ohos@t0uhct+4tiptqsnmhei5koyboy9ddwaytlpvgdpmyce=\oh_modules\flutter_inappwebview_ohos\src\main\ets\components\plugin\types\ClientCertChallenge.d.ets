import URLAuthenticationChallenge from './URLAuthenticationChallenge';
import URLProtectionSpace from './URLProtectionSpace';
export default class ClientCertChallenge extends URLAuthenticationChallenge {
    private principals;
    private keyTypes;
    constructor(protectionSpace: URLProtectionSpace, principals: Array<string> | null, keyTypes: Array<string> | null);
    toMap(): Promise<Map<string, any>>;
    getPrincipals(): Array<string> | null;
    setPrincipals(principals: Array<string> | null): void;
    getKeyTypes(): Array<string> | null;
    setKeyTypes(keyTypes: Array<string> | null): void;
}
