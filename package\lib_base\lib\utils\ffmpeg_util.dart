import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:lib_base/log/log.dart';
import 'package:lib_base/model/http/video_info_model.dart';
import 'package:lib_base/resource/plugins/ffmpeg_plugin.dart';
import 'package:lib_base/utils/business/file_util.dart';
import 'package:lib_base/utils/business/image_util.dart';
import 'package:lib_base/utils/ui_util.dart';

class FFMpegUtil {
  static FFmpegExecutor executor = FFmpegExecutor();

  //合并录音的音频，  先将 录音音频和backfile 叠加，  然后替换对应时间段的录音音频与合配音频拼接
  static Future doMergeCooperAudio(
      String backFile,
      String cooperFile,
      List<String> records,
      List<VideoInfoModel> videos,
      String output,
      String videoDuration) async {
    //将录音音频和backfile叠加
    String tempMergedPath =
        "${File(backFile).parent.path}/tempMergeAudo.${output.split('.').last}";
    File tempFile = File(tempMergedPath);
    if (tempFile.existsSync()) {
      tempFile.deleteSync();
    }

    await doMergeAudio(backFile, records, videos, tempMergedPath);
    await Future.delayed(Duration(milliseconds: 100));
    if (!File(tempMergedPath).existsSync()) {
      showToast("操作失败");
      Logger.info("===============  操作失败");
      // return;
    }
    double videoLength = double.tryParse(videoDuration) ?? 0;
    // ffmpeg -i a.wav -i b.wav -filter_complex \
    // "[0]atrim=start=0:end=1,asetpts=PTS-STARTPTS[a1]; \
    // [0]atrim=start=2:end=3,asetpts=PTS-STARTPTS[a2]; \
    // [0]atrim=start=4:end=5,asetpts=PTS-STARTPTS[a3]; \
    // [0]atrim=start=6,asetpts=PTS-STARTPTS[a4]; \
    // [1]atrim=start=1:end=2,asetpts=PTS-STARTPTS[b1]; \
    // [1]atrim=start=3:end=4,asetpts=PTS-STARTPTS[b2]; \
    // [1]atrim=start=5:end=6,asetpts=PTS-STARTPTS[b3]; \
    // [a1][a2][a3][b1][b2][b3][a4]concat=n=7:v=0:a=1" output.wav
    String commandStr = " -i $cooperFile -i $tempMergedPath  -filter_complex  ";

    //将叠加后的音频切段， 保留records 对应的时间段的片段，
    //将需要合配的音频切段， 丢弃records对应的时间段的片段

    String atrim0Str = "";
    String atrim1Str = "";
    String appendStr = "";
    double startA = 0;
    //音频段数
    int asize = 0;
    for (int i = 0; i < videos.length; i++) {
      VideoInfoModel video = videos[i];
      String startPoint = video.startPoint ?? "";
      String endPoint = video.endPoint ?? "";
      double? start = double.tryParse(startPoint);
      double? end = double.tryParse(endPoint);
      if (start != null && end != null) {
        double endA = start;
        double startB = start;
        double endB = end;
        if (start > 0 && startA < endA) {
          String atrim0 =
              "[0]atrim=start=$startA:end=$endA,asetpts=PTS-STARTPTS[a$i];  ";
          atrim0Str = "$atrim0Str $atrim0";
        }
        Logger.info(
            "startPoint:$startPoint, endPoint:$endPoint, startA:$startA, endA:$endA, startB:$startB,endB:$endB");
        String atrim1 =
            "[1]atrim=start=$startB:end=$endB,asetpts=PTS-STARTPTS[b$i];  ";
        atrim1Str = "$atrim1Str $atrim1";
        if (start > 0 && startA < endA) {
          asize++;
          appendStr = "$appendStr[a$i]";
        }
        asize++;
        appendStr = "$appendStr[b$i]";
        if (i == videos.length - 1) {
          //最后一个， 再拼接一段、、、、、、、、、、、、、、、、、、
          if (videoLength > end) {
            String atrim0 =
                "[0]atrim=start=$end,asetpts=PTS-STARTPTS[a${i + 1}];  ";
            atrim0Str = "$atrim0Str $atrim0";
            asize++;
            appendStr = "$appendStr[a${i + 1}]";
          }
        }
        startA = end;
      }
    }
    String atrimCommandStr =
        "\" $atrim0Str $atrim1Str $appendStr  concat=n=${asize}:v=0:a=1  \"";
    commandStr = "$commandStr $atrimCommandStr $output";
    Logger.info("======== commandStr:$commandStr");

    var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
  }

  //合并录音的音频，  录音音频和backfile 叠加
  //backfile 背景音频，  output 合并后的音频
  static Future doMergeAudio(String backFile, List<String> records,
      List<VideoInfoModel> videos, String output) async {
    // duration=longest
    // String commandStr=" -i $backFile -i ${records[0]} -i ${records[1]} -filter_complex \"[1]adelay=1000|1000[a1];[2]adelay=3000|3000[a2];  [0][a1][a2]amix=inputs=3:duration=first \"  $output "
    //  ;
    if(!File(backFile).existsSync()){
       Logger.error("============== backFile ${backFile} 不存在");
      return;
    }
    String commandStr = " -i $backFile";
    //拼接输入音频
    String delayStr = "";
    String amixStr = "[0]";
    //拼接文件数量
    int index = 0;
    for (int i = 0; i < videos.length; i++) {
      VideoInfoModel video = videos[i];
      String file = records[i];
      Logger.info("============file:${file} exist:${File(file).existsSync()}，  ");
      String startPoint = video.startPoint ?? "";
      double? start = double.tryParse(startPoint);
      if (start != null) {
        int delay = (start * 1000).toInt();
        index = (index + 1);

        commandStr = " $commandStr -i $file ";

        delayStr = " $delayStr  [$index]adelay=$delay|$delay[a$index];";
        amixStr = " $amixStr[a$index]";
      }
    }
    amixStr = "${amixStr}amix=inputs=${index + 1}:duration=first \"  $output";
    commandStr = " $commandStr -filter_complex \"";
    commandStr = "$commandStr $delayStr $amixStr";

    Logger.info("====== ffmpeg command: $commandStr");
    // await FFmpegKit.execute(commandStr).then((session) async {
    //   final ReturnCode? returnCode = await session.getReturnCode();
    //   Logger.info(" return code: ${returnCode?.getValue()}, output:${ await session.getOutput()}");
    // });
    var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
    Logger.info("========== ffmpeg execute result: $result");
  }

  static Future mergeAudioAndVideo(
      String audioPath, String videoPath, String output) async {
    String commandStr =
        " -i $videoPath -i $audioPath -c:v copy -c:a aac $output";
    var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
    Logger.info("ffmpeg execute,commandStr:$commandStr,  result: $result");
    // await FFmpegKit.execute(commandStr).then((session) async {
    //   final ReturnCode? returnCode = await session.getReturnCode();
    //   Logger.info(" return code: ${returnCode?.getValue()}, output:${ await session.getOutput()}");
    // });
  }

  static Future parsePcmToWav(String pcmFile, String wavOutput) async {
    String commandStr = "  -f s16le -ar 16000 -ac 1 -i $pcmFile $wavOutput ";
    var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
    Logger.info("ffmpeg execute,commandStr:$commandStr,  result: $result");
  }

  //获取视频第一帧做缩略图
  static Future<String?> getVedioFirstFrame(
    String videoPath, {
    String? savePath,
  }) async {
    savePath = savePath ??
        '${await FileUtil.businessFolderPath()}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    
    // 创建一个Completer来等待任务完成
    Completer<String?> completer = Completer<String?>();
    
    // 添加任务到执行器
    executor.addTask(() async {
      try {
        String result = await _getVideoFIrstFram(videoPath, savePath: savePath);
        completer.complete(result);
      } catch (e) {
        Logger.error("获取视频第一帧失败: $e");
        completer.complete(); // 即使失败也返回路径
      }
    });
    
    // 等待任务完成并返回结果
    return completer.future;
  }

  static Future<String> _getVideoFIrstFram(
    String videoPath, {
    String? savePath,
  }) async {
    savePath = savePath ??
        '${await FileUtil.businessFolderPath()}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    String commandStr = "  -i $videoPath -vframes 1  $savePath";
    var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
    Logger.info(
        "ffmpeg execute,commandStr:$commandStr,  result: $result， savePath exist:${File(savePath).existsSync()}");
    // if(result.toString()=='1'){
    //   await getVedioFirstFrame1(videoPath, savePath: savePath);
    // }
    return savePath;
  }

  static Future downloadFile(String url, String path) async {
    try {
      Dio dio = Dio();

      Logger.info("downloadVedio===$path");
      await dio.download(ImageUtil.getImageUrl(url), path);
    } catch (e, statcktrace) {
      Logger.error("下载失败,e:${e.toString()}", statcktrace);
    }
  }

  //获取网络视频第一帧做缩略图
  static Future<String?> getNetVedioFirstFrame(
    String videourl, {
    String? savePath,
    String? tempName,
  }) async {
    savePath = savePath ??
        '${await FileUtil.businessFolderPath()}/${DateTime.now().millisecondsSinceEpoch}.jpg';
    //下载视频
    String path =
        '${await FileUtil.businessFolderPath()}/${tempName ?? '${DateTime.now().millisecondsSinceEpoch}.mp4'}';
    if (File(path).existsSync()) {
      File(path).deleteSync();
    }
    Logger.info("============ start to download");
    await downloadFile(videourl, path);
    Logger.info(
        "============     download finished, file exist:${File(path).existsSync()}");
    // EyybOssController().doUploadByRest(files: [path], ossUploadItem:   OssUploadItem.UPLOAD_RECORD);
    return getVedioFirstFrame(path, savePath: savePath);
  }

  //这个压缩有问题
  // static Future<String> compressVideo(
  //     String originPath, String compressedPath) async {
  //   assert(originPath.isNotEmpty && compressedPath.isNotEmpty,
  //       "originPath or compressedPath is empty");
  //   String commandStr =
  //       "-i $originPath -vf \"scale='min(1280,iw)':-2\" -b:v 1500k    $compressedPath";
  //   var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
  //   Logger.info("ffmpeg execute,commandStr:$commandStr,  result: $result");
  //   return compressedPath;
  // }

  static Future<String> compressImage(
      String originPath, String compressedPath) async {
    assert(originPath.isNotEmpty && compressedPath.isNotEmpty,
        "originPath or compressedPath is empty");
    String commandStr =
        "-i $originPath -vf \"scale='min(1280,iw)':-2\" -q:v 8  $compressedPath";
    var result = await FFmpegPlugin.executeFFmpegCmd(commandStr);
    Logger.info("ffmpeg execute,commandStr:$commandStr,  result: $result");
    return compressedPath;
  }
}

typedef FfmpegTask = Future Function();

//ffmpeg 执行器， 鸿蒙ffmpeg不能并行执行
class FFmpegExecutor {
  bool isRunning = false;

  List<FfmpegTask> tasks = [];

  void addTask(FfmpegTask task) {
    tasks.add(task);
    if (!isRunning) {
      isRunning = true;
      _executeTask();
    }

  }

   
 

  Future _executeTask() async {
    if (tasks.isNotEmpty) {
      FfmpegTask task = tasks.removeAt(0);
      await task.call();
      _executeTask();
    } else {
      isRunning = false;
    }
  }
}
