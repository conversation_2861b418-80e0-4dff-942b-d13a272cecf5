import ChannelDelegateImpl from './types/ChannelDelegateImpl';
import InAppWebViewFlutterPlugin from './InAppWebViewFlutterPlugin';
import { Any, MethodCall } from '@ohos/flutter_ohos';
import { MethodResult } from '@ohos/flutter_ohos/src/main/ets/plugin/common/MethodChannel';
import List from "@ohos.util.List";
export default class MyCookieManager extends ChannelDelegateImpl {
    plugin: InAppWebViewFlutterPlugin | null;
    constructor(plugin: InAppWebViewFlutterPlugin);
    onMethodCall(call: MethodCall, result: MethodResult): Promise<void>;
    setCookie(url: string, name: string, value: string, domain: string, path: string, expiresDate: number | null, maxAge: number, isSecure: boolean, isHttpOnly: boolean, sameSite: string, result: MethodResult): void;
    getCookies(url: string): Promise<List<Map<string, Any>>>;
    deleteCookie(url: string, name: string, domain: string, path: string, result: MethodResult): void;
    deleteCookies(url: string, domain: string, path: string, result: MethodResult): void;
    deleteAllCookies(result: MethodResult): void;
    removeSessionCookies(result: MethodResult): void;
    getCookieExpirationDate(timestamp: number): string;
    dispose(): void;
}
