import { Any } from '@ohos/flutter_ohos';
import ISettings from '../ISettings';
import MediaSizeExt from '../types/MediaSizeExt';
import ResolutionExt from '../types/ResolutionExt';
import PrintJobController from './PrintJobController';
export default class PrintJobSettings implements ISettings<PrintJobController> {
    LOG_TAG: string;
    handledByClient: boolean;
    jobName: string | null;
    orientation: number | null;
    mediaSize: MediaSizeExt | null;
    colorMode: number | null;
    duplexMode: number | null;
    resolution: ResolutionExt | null;
    constructor();
    parse(settings: Map<string, Any>): PrintJobSettings;
    toMap(): Map<string, Any>;
    getRealSettings(printJobController: PrintJobController): Map<string, Any>;
}
